<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkLogin();

$user = getCurrentUser();
$conn = getDBConnection();

// إحصائيات بسيطة
$today = date('Y-m-d');

try {
    // عدد المهام اليوم
    if ($user['role'] === 'admin') {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE date = ?");
        $stmt->execute([$today]);
    } else {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE date = ? AND user_id = ?");
        $stmt->execute([$today, $user['id']]);
    }
    $tasks_today = $stmt->fetch()['count'];

    // عدد المهام المكتملة اليوم
    if ($user['role'] === 'admin') {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE date = ? AND status = 'منجز'");
        $stmt->execute([$today]);
    } else {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE date = ? AND status = 'منجز' AND user_id = ?");
        $stmt->execute([$today, $user['id']]);
    }
    $completed_tasks = $stmt->fetch()['count'];

    // عدد الإنجازات اليوم
    if ($user['role'] === 'admin') {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM achievements WHERE date = ?");
        $stmt->execute([$today]);
    } else {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM achievements WHERE date = ? AND user_id = ?");
        $stmt->execute([$today, $user['id']]);
    }
    $achievements_today = $stmt->fetch()['count'];

    $page_title = 'لوحة التحكم';
    include 'includes/header.php';
    ?>

    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    لوحة التحكم
                </h1>
                <div class="text-muted">
                    <i class="fas fa-calendar-day me-1"></i>
                    <?php echo date('Y-m-d'); ?>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3><?php echo $tasks_today; ?></h3>
                    <p class="mb-0">مهام اليوم</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3><?php echo $completed_tasks; ?></h3>
                    <p class="mb-0">مهام مكتملة</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3><?php echo $achievements_today; ?></h3>
                    <p class="mb-0">إنجازات اليوم</p>
                </div>
            </div>
        </div>
    </div>

    <!-- الأعمال السريعة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-bolt me-2"></i>
                    الأعمال السريعة
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="tasks.php?action=add" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>
                                إضافة مهمة جديدة
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="achievements.php?action=add" class="btn btn-success w-100">
                                <i class="fas fa-trophy me-2"></i>
                                إضافة إنجاز جديد
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="tasks.php" class="btn btn-info w-100">
                                <i class="fas fa-list me-2"></i>
                                عرض جميع المهام
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="achievements.php" class="btn btn-warning w-100">
                                <i class="fas fa-star me-2"></i>
                                عرض الإنجازات
                            </a>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <a href="reports.php" class="btn btn-secondary w-100">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير الشهرية
                            </a>
                        </div>
                        <div class="col-md-6 mb-2">
                            <a href="reports.php?type=daily&date=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-calendar-day me-2"></i>
                                تقرير اليوم
                            </a>
                        </div>
                    </div>
                    <?php if ($user['role'] === 'admin'): ?>
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <a href="users_management.php" class="btn btn-info w-100">
                                <i class="fas fa-users me-2"></i>
                                إدارة المنتسبين
                            </a>
                        </div>
                        <div class="col-md-6 mb-2">
                            <a href="evaluations.php" class="btn btn-warning w-100">
                                <i class="fas fa-star me-2"></i>
                                تقييم المنتسبين
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="alert alert-success">
                <h4>مرحباً <?php echo htmlspecialchars($user['full_name']); ?>!</h4>
                <p>النظام يعمل بشكل صحيح. يمكنك الآن البدء في استخدام جميع المميزات.</p>
                <hr>
                <p class="mb-0">
                    <a href="dashboard.php" class="btn btn-primary">الانتقال إلى لوحة التحكم الكاملة</a>
                </p>
            </div>
        </div>
    </div>

    <?php
    include 'includes/footer.php';

} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>خطأ في النظام</h4>";
    echo "<p>الرسالة: " . $e->getMessage() . "</p>";
    echo "<p>الملف: " . $e->getFile() . "</p>";
    echo "<p>السطر: " . $e->getLine() . "</p>";
    echo "<p><a href='debug.php' class='btn btn-warning'>تشخيص المشكلة</a></p>";
    echo "</div>";
}
?>
