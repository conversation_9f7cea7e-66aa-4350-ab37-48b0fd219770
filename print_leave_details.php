<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkLogin();

$user = getCurrentUser();
$conn = getDBConnection();

$user_id = $_GET['user_id'] ?? ($user['role'] === 'admin' ? null : $user['id']);
$year = $_GET['year'] ?? date('Y');
$print_mode = isset($_GET['print']);

if (!$user_id) {
    $_SESSION['error'] = 'يجب تحديد المنتسب';
    header('Location: leave_balance.php');
    exit();
}

// التحقق من الصلاحيات
if ($user['role'] !== 'admin' && $user_id != $user['id']) {
    $_SESSION['error'] = 'ليس لديك صلاحية لعرض هذه البيانات';
    header('Location: user_dashboard.php');
    exit();
}

// جلب بيانات المنتسب
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ? AND role = 'user'");
$stmt->execute([$user_id]);
$selected_user = $stmt->fetch();

if (!$selected_user) {
    $_SESSION['error'] = 'المنتسب غير موجود';
    header('Location: leave_balance.php');
    exit();
}

// جلب رصيد الإجازات
$stmt = $conn->prepare("
    SELECT alb.*, lt.name as leave_type_name, lt.description
    FROM annual_leave_balance alb
    JOIN leave_types lt ON alb.leave_type_id = lt.id
    WHERE alb.user_id = ? AND alb.year = ?
    ORDER BY lt.name
");
$stmt->execute([$user_id, $year]);
$leave_balances = $stmt->fetchAll();

// جلب تفاصيل الإجازات المستخدمة
$stmt = $conn->prepare("
    SELECT lr.*, lt.name as leave_type_name, approver.full_name as approved_by_name
    FROM leave_requests lr
    JOIN leave_types lt ON lr.leave_type_id = lt.id
    LEFT JOIN users approver ON lr.approved_by = approver.id
    WHERE lr.user_id = ? AND YEAR(lr.start_date) = ?
    ORDER BY lr.start_date DESC
");
$stmt->execute([$user_id, $year]);
$leave_requests = $stmt->fetchAll();

// جلب تفاصيل الحضور للسنة
$stmt = $conn->prepare("
    SELECT 
        date,
        status,
        check_in_time,
        check_out_time,
        notes
    FROM attendance 
    WHERE user_id = ? AND YEAR(date) = ? AND status IN ('إجازة', 'إيفاد', 'مرض')
    ORDER BY date DESC
");
$stmt->execute([$user_id, $year]);
$attendance_leaves = $stmt->fetchAll();

// حساب إحصائيات الإجازات
$total_allocated = array_sum(array_column($leave_balances, 'total_days'));
$total_used = array_sum(array_column($leave_balances, 'used_days'));
$total_remaining = array_sum(array_column($leave_balances, 'remaining_days'));

$page_title = 'تفاصيل الإجازات - ' . $selected_user['full_name'];

if (!$print_mode) {
    include 'includes/header.php';
}
?>

<?php if ($print_mode): ?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الإجازات - <?php echo htmlspecialchars($selected_user['full_name']); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }
        
        .print-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 0 30px rgba(0,0,0,0.2);
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #667eea;
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        
        .user-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 5px solid #667eea;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .section {
            margin: 40px 0;
            page-break-inside: avoid;
        }
        
        .section-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: bold;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }
        
        .table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            text-align: center;
        }
        
        .table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .status-approved { background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; }
        .status-pending { background: #ffc107; color: black; padding: 5px 10px; border-radius: 15px; }
        .status-rejected { background: #dc3545; color: white; padding: 5px 10px; border-radius: 15px; }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            height: 25px;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .signature-section {
            margin-top: 50px;
            padding-top: 30px;
            border-top: 2px solid #667eea;
            text-align: center;
            page-break-inside: avoid;
        }
        
        .signature-line {
            border-bottom: 2px solid #333;
            width: 200px;
            margin: 20px auto;
            padding-bottom: 5px;
        }
        
        @media print {
            body { margin: 0; background: white !important; }
            .print-container { box-shadow: none; }
            .section { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="header">
            <h1>شعبة السيطرة والإنترنت</h1>
            <h2>تفاصيل الإجازات والأوقات الممنوحة</h2>
        </div>
        
        <div class="user-info">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <strong>اسم المنتسب:</strong> <?php echo htmlspecialchars($selected_user['full_name']); ?><br>
                    <strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($selected_user['username']); ?><br>
                    <strong>السنة:</strong> <?php echo $year; ?>
                </div>
                <div>
                    <strong>إجمالي الأيام المخصصة:</strong> <?php echo $total_allocated; ?> يوم<br>
                    <strong>الأيام المستخدمة:</strong> <?php echo $total_used; ?> يوم<br>
                    <strong>تاريخ التقرير:</strong> <?php echo date('Y/m/d H:i'); ?>
                </div>
            </div>
        </div>

        <!-- إحصائيات الإجازات -->
        <div class="section">
            <div class="section-title">📊 إحصائيات الإجازات</div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_allocated; ?></div>
                    <div>إجمالي الأيام المخصصة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_used; ?></div>
                    <div>الأيام المستخدمة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_remaining; ?></div>
                    <div>الأيام المتبقية</div>
                </div>
            </div>
        </div>

        <!-- رصيد الإجازات التفصيلي -->
        <div class="section">
            <div class="section-title">💰 رصيد الإجازات التفصيلي</div>
            
            <?php if (!empty($leave_balances)): ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>نوع الإجازة</th>
                            <th>الوصف</th>
                            <th>الأيام المخصصة</th>
                            <th>الأيام المستخدمة</th>
                            <th>الأيام المتبقية</th>
                            <th>نسبة الاستخدام</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($leave_balances as $balance): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($balance['leave_type_name']); ?></strong></td>
                                <td><?php echo htmlspecialchars($balance['description']); ?></td>
                                <td><?php echo $balance['total_days']; ?></td>
                                <td><?php echo $balance['used_days']; ?></td>
                                <td><?php echo $balance['remaining_days']; ?></td>
                                <td>
                                    <?php 
                                    $usage_percent = $balance['total_days'] > 0 ? round(($balance['used_days'] / $balance['total_days']) * 100, 1) : 0;
                                    ?>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: <?php echo $usage_percent; ?>%">
                                            <?php echo $usage_percent; ?>%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p style="text-align: center; color: #666;">لا توجد بيانات رصيد إجازات</p>
            <?php endif; ?>
        </div>

        <!-- طلبات الإجازات -->
        <div class="section">
            <div class="section-title">📝 سجل طلبات الإجازات</div>
            
            <?php if (!empty($leave_requests)): ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>نوع الإجازة</th>
                            <th>من</th>
                            <th>إلى</th>
                            <th>عدد الأيام</th>
                            <th>السبب</th>
                            <th>الحالة</th>
                            <th>الموافق</th>
                            <th>تاريخ الطلب</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($leave_requests as $request): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($request['leave_type_name']); ?></td>
                                <td><?php echo date('Y/m/d', strtotime($request['start_date'])); ?></td>
                                <td><?php echo date('Y/m/d', strtotime($request['end_date'])); ?></td>
                                <td><?php echo $request['days_count']; ?></td>
                                <td><?php echo htmlspecialchars(substr($request['reason'], 0, 30)) . (strlen($request['reason']) > 30 ? '...' : ''); ?></td>
                                <td>
                                    <span class="status-<?php echo $request['status'] === 'موافق' ? 'approved' : ($request['status'] === 'معلق' ? 'pending' : 'rejected'); ?>">
                                        <?php echo $request['status']; ?>
                                    </span>
                                </td>
                                <td><?php echo $request['approved_by_name'] ? htmlspecialchars($request['approved_by_name']) : '-'; ?></td>
                                <td><?php echo date('Y/m/d', strtotime($request['created_at'])); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p style="text-align: center; color: #666;">لا توجد طلبات إجازات</p>
            <?php endif; ?>
        </div>

        <!-- سجل الحضور للإجازات -->
        <div class="section">
            <div class="section-title">📅 سجل الحضور للإجازات والإيفادات</div>
            
            <?php if (!empty($attendance_leaves)): ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>الحالة</th>
                            <th>وقت الدخول</th>
                            <th>وقت الخروج</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($attendance_leaves as $attendance): ?>
                            <tr>
                                <td><?php echo date('Y/m/d', strtotime($attendance['date'])); ?></td>
                                <td>
                                    <span class="status-<?php echo $attendance['status'] === 'إجازة' ? 'approved' : 'pending'; ?>">
                                        <?php echo $attendance['status']; ?>
                                    </span>
                                </td>
                                <td><?php echo $attendance['check_in_time'] ?: '-'; ?></td>
                                <td><?php echo $attendance['check_out_time'] ?: '-'; ?></td>
                                <td><?php echo htmlspecialchars($attendance['notes'] ?: '-'); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p style="text-align: center; color: #666;">لا توجد سجلات إجازات في الحضور</p>
            <?php endif; ?>
        </div>
        
        <div class="signature-section">
            <p><strong>مسؤول شعبة السيطرة والإنترنت</strong></p>
            <div class="signature-line"></div>
            <p><strong>محمد حسن محمد</strong></p>
            <p style="margin-top: 30px; color: #666;">
                تم إنشاء هذا التقرير بواسطة نظام إدارة شعبة السيطرة والإنترنت<br>
                تاريخ الطباعة: <?php echo date('Y/m/d H:i:s'); ?>
            </p>
        </div>
    </div>
    
    <script>
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>

<?php else: ?>
<!-- واجهة اختيار المنتسب والسنة -->
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-calendar-alt me-2"></i>
                طباعة تفاصيل الإجازات
            </h1>
            <a href="leave_balance.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                العودة لرصيد الإجازات
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-user-clock me-2"></i>
                اختيار المنتسب والسنة
            </div>
            <div class="card-body">
                <form method="GET" action="">
                    <div class="row">
                        <?php if ($user['role'] === 'admin'): ?>
                        <div class="col-md-6 mb-3">
                            <label for="user_id" class="form-label">المنتسب *</label>
                            <select class="form-select" id="user_id" name="user_id" required>
                                <option value="">اختر المنتسب</option>
                                <?php
                                $stmt = $conn->prepare("SELECT id, full_name FROM users WHERE role = 'user' ORDER BY full_name");
                                $stmt->execute();
                                $all_users = $stmt->fetchAll();
                                
                                foreach ($all_users as $u):
                                ?>
                                    <option value="<?php echo $u['id']; ?>" <?php echo $user_id == $u['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($u['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <?php else: ?>
                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                        <?php endif; ?>
                        
                        <div class="col-md-6 mb-3">
                            <label for="year" class="form-label">السنة *</label>
                            <select class="form-select" id="year" name="year" required>
                                <?php for ($y = date('Y') - 3; $y <= date('Y') + 1; $y++): ?>
                                    <option value="<?php echo $y; ?>" <?php echo $year == $y ? 'selected' : ''; ?>>
                                        <?php echo $y; ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-eye me-2"></i>
                            معاينة التقرير
                        </button>
                        <?php if ($user_id): ?>
                            <a href="print_leave_details.php?user_id=<?php echo $user_id; ?>&year=<?php echo $year; ?>&print=1" 
                               class="btn btn-success" target="_blank">
                                <i class="fas fa-print me-2"></i>
                                طباعة التقرير
                            </a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php if ($user_id && $selected_user): ?>
<!-- معاينة التقرير -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>
                    <i class="fas fa-calendar-check me-2"></i>
                    معاينة تفاصيل الإجازات - <?php echo htmlspecialchars($selected_user['full_name']); ?> (<?php echo $year; ?>)
                </span>
                <a href="print_leave_details.php?user_id=<?php echo $user_id; ?>&year=<?php echo $year; ?>&print=1" 
                   class="btn btn-success" target="_blank">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </a>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $total_allocated; ?></h4>
                                <p class="mb-0">أيام مخصصة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $total_used; ?></h4>
                                <p class="mb-0">أيام مستخدمة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $total_remaining; ?></h4>
                                <p class="mb-0">أيام متبقية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h4><?php echo count($leave_requests); ?></h4>
                                <p class="mb-0">طلبات إجازات</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>محتويات التقرير:</strong>
                    رصيد الإجازات التفصيلي، سجل طلبات الإجازات، سجل الحضور للإجازات والإيفادات مع الأوقات الممنوحة
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php endif; ?>

<?php if (!$print_mode) include 'includes/footer.php'; ?>
