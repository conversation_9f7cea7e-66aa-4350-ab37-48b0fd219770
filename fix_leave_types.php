<?php
// إصلاح سريع لأنواع الإجازات المتطورة
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إصلاح أنواع الإجازات</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }";
echo ".container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); max-width: 800px; margin: 0 auto; }";
echo ".success { color: green; padding: 10px; background: #e8f5e8; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: red; padding: 10px; background: #ffe6e6; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: blue; padding: 10px; background: #e8f4fd; border-radius: 5px; margin: 10px 0; }";
echo ".btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block; }";
echo ".btn-success { background: #28a745; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔧 إصلاح أنواع الإجازات المتطورة</h1>";

try {
    $conn = getDBConnection();
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // 1. إضافة الحقول الجديدة إذا لم تكن موجودة
    echo "<h3>📋 إضافة الحقول الجديدة...</h3>";
    
    $new_columns = [
        "is_deductible BOOLEAN DEFAULT TRUE COMMENT 'هل تستقطع من الرصيد'",
        "is_unlimited BOOLEAN DEFAULT FALSE COMMENT 'غير محدودة'",
        "requires_approval BOOLEAN DEFAULT TRUE COMMENT 'تحتاج موافقة'",
        "can_be_hourly BOOLEAN DEFAULT FALSE COMMENT 'يمكن أخذها بالساعة'",
        "priority_order INT DEFAULT 1 COMMENT 'ترتيب الأولوية'",
        "color_code VARCHAR(7) DEFAULT '#007bff' COMMENT 'لون التمييز'",
        "icon VARCHAR(50) DEFAULT 'fas fa-calendar' COMMENT 'أيقونة'"
    ];
    
    foreach ($new_columns as $column_sql) {
        try {
            $conn->exec("ALTER TABLE leave_types ADD COLUMN $column_sql");
            echo "<div class='success'>✅ تم إضافة حقل جديد</div>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<div class='info'>ℹ️ الحقل موجود بالفعل</div>";
            } else {
                echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    // 2. حذف الأنواع القديمة
    echo "<h3>🗑️ حذف الأنواع القديمة...</h3>";
    $conn->exec("DELETE FROM leave_types");
    echo "<div class='info'>ℹ️ تم حذف جميع أنواع الإجازات القديمة</div>";
    
    // 3. إضافة أنواع الإجازات الجديدة
    echo "<h3>➕ إضافة أنواع الإجازات الجديدة...</h3>";
    
    $new_leave_types = [
        [
            'name' => 'إجازة سنوية',
            'max_days_per_year' => 30,
            'description' => 'الإجازة السنوية العادية - 30 يوم سنوياً',
            'is_deductible' => 1,
            'is_unlimited' => 0,
            'requires_approval' => 1,
            'can_be_hourly' => 0,
            'priority_order' => 1,
            'color_code' => '#28a745',
            'icon' => 'fas fa-calendar-alt'
        ],
        [
            'name' => 'إجازة مرضية',
            'max_days_per_year' => 0,
            'description' => 'إجازة للحالات المرضية - غير محدودة',
            'is_deductible' => 0,
            'is_unlimited' => 1,
            'requires_approval' => 1,
            'can_be_hourly' => 0,
            'priority_order' => 2,
            'color_code' => '#dc3545',
            'icon' => 'fas fa-user-injured'
        ],
        [
            'name' => 'حالة وفاة',
            'max_days_per_year' => 0,
            'description' => 'إجازة حالة وفاة - غير محدودة',
            'is_deductible' => 0,
            'is_unlimited' => 1,
            'requires_approval' => 1,
            'can_be_hourly' => 0,
            'priority_order' => 3,
            'color_code' => '#6c757d',
            'icon' => 'fas fa-heart-broken'
        ],
        [
            'name' => 'إيفاد',
            'max_days_per_year' => 0,
            'description' => 'إيفاد رسمي للعمل - غير محدود',
            'is_deductible' => 0,
            'is_unlimited' => 1,
            'requires_approval' => 1,
            'can_be_hourly' => 0,
            'priority_order' => 4,
            'color_code' => '#17a2b8',
            'icon' => 'fas fa-briefcase'
        ],
        [
            'name' => 'ساعة طارئة',
            'max_days_per_year' => 0,
            'description' => 'ساعة زمنية للحالات الطارئة',
            'is_deductible' => 0,
            'is_unlimited' => 1,
            'requires_approval' => 1,
            'can_be_hourly' => 1,
            'priority_order' => 5,
            'color_code' => '#ffc107',
            'icon' => 'fas fa-clock'
        ],
        [
            'name' => 'إجازة أمومة',
            'max_days_per_year' => 90,
            'description' => 'إجازة الأمومة للموظفات - 90 يوم',
            'is_deductible' => 0,
            'is_unlimited' => 0,
            'requires_approval' => 1,
            'can_be_hourly' => 0,
            'priority_order' => 6,
            'color_code' => '#e83e8c',
            'icon' => 'fas fa-baby'
        ],
        [
            'name' => 'إجازة أبوة',
            'max_days_per_year' => 3,
            'description' => 'إجازة الأبوة للموظفين - 3 أيام',
            'is_deductible' => 0,
            'is_unlimited' => 0,
            'requires_approval' => 1,
            'can_be_hourly' => 0,
            'priority_order' => 7,
            'color_code' => '#6f42c1',
            'icon' => 'fas fa-male'
        ]
    ];
    
    $stmt = $conn->prepare("
        INSERT INTO leave_types (
            name, max_days_per_year, description, is_deductible, is_unlimited, 
            requires_approval, can_be_hourly, priority_order, color_code, icon
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    foreach ($new_leave_types as $type) {
        $stmt->execute([
            $type['name'], $type['max_days_per_year'], $type['description'],
            $type['is_deductible'], $type['is_unlimited'], $type['requires_approval'],
            $type['can_be_hourly'], $type['priority_order'], $type['color_code'], $type['icon']
        ]);
        echo "<div class='success'>✅ تم إضافة: " . $type['name'] . "</div>";
    }
    
    // 4. إعادة تهيئة أرصدة الإجازات
    echo "<h3>🔄 إعادة تهيئة أرصدة الإجازات...</h3>";
    
    // حذف الأرصدة القديمة
    $conn->exec("DELETE FROM annual_leave_balance");
    echo "<div class='info'>ℹ️ تم حذف الأرصدة القديمة</div>";
    
    // جلب المنتسبين
    $stmt = $conn->prepare("SELECT id FROM users WHERE role = 'user'");
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    // جلب أنواع الإجازات الجديدة
    $stmt = $conn->prepare("SELECT id, max_days_per_year, is_unlimited FROM leave_types");
    $stmt->execute();
    $leave_types = $stmt->fetchAll();
    
    $current_year = date('Y');
    $balance_stmt = $conn->prepare("
        INSERT INTO annual_leave_balance (
            user_id, year, leave_type_id, total_days, used_days, remaining_days, last_reset_date
        ) VALUES (?, ?, ?, ?, 0, ?, ?)
    ");
    
    $total_balances = 0;
    foreach ($users as $user) {
        foreach ($leave_types as $type) {
            $total_days = $type['is_unlimited'] ? 999 : $type['max_days_per_year'];
            $balance_stmt->execute([
                $user['id'], $current_year, $type['id'], 
                $total_days, $total_days, $current_year . '-01-01'
            ]);
            $total_balances++;
        }
    }
    
    echo "<div class='success'>✅ تم إنشاء $total_balances رصيد إجازة</div>";
    
    // 5. التحقق من النتائج
    echo "<h3>🔍 التحقق من النتائج...</h3>";
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM leave_types");
    $stmt->execute();
    $types_count = $stmt->fetch()['count'];
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM annual_leave_balance WHERE year = ?");
    $stmt->execute([$current_year]);
    $balances_count = $stmt->fetch()['count'];
    
    echo "<div class='success'>✅ تم إنشاء $types_count نوع إجازة</div>";
    echo "<div class='success'>✅ تم إنشاء $balances_count رصيد إجازة</div>";
    
    // عرض أنواع الإجازات الجديدة
    echo "<h3>📋 أنواع الإجازات الجديدة:</h3>";
    $stmt = $conn->prepare("SELECT * FROM leave_types ORDER BY priority_order");
    $stmt->execute();
    $types = $stmt->fetchAll();
    
    echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>النوع</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>الحد الأقصى</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>يستقطع</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>بالساعة</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>الأولوية</th>";
    echo "</tr>";
    
    foreach ($types as $type) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 10px;'>";
        echo "<span style='color: " . $type['color_code'] . ";'>";
        echo "<i class='" . $type['icon'] . "'></i> " . $type['name'];
        echo "</span></td>";
        echo "<td style='border: 1px solid #ddd; padding: 10px;'>" . ($type['is_unlimited'] ? 'غير محدود' : $type['max_days_per_year'] . ' يوم') . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 10px;'>" . ($type['is_deductible'] ? 'نعم' : 'لا') . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 10px;'>" . ($type['can_be_hourly'] ? 'نعم' : 'لا') . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 10px;'>" . $type['priority_order'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 30px 0; text-align: center;'>";
    echo "<h2>🎉 تم الإصلاح بنجاح!</h2>";
    echo "<p>نظام الإجازات المتطور جاهز للاستخدام</p>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='leave_requests.php' class='btn btn-success'>📝 طلبات الإجازات</a>";
    echo "<a href='leave_balance.php' class='btn btn-success'>💰 رصيد الإجازات</a>";
    echo "<a href='activate_advanced_leave_system.php' class='btn'>🔄 التحقق من النظام</a>";
    echo "<a href='dashboard.php' class='btn'>🏠 لوحة التحكم</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في الإصلاح: " . $e->getMessage() . "</div>";
    
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>💡 تأكد من:</h3>";
    echo "<ul>";
    echo "<li>تشغيل XAMPP (Apache + MySQL)</li>";
    echo "<li>وجود قاعدة البيانات control_internet_db</li>";
    echo "<li>وجود جدول leave_types</li>";
    echo "<li>صلاحيات التعديل في قاعدة البيانات</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><a href='setup_attendance.php' class='btn btn-success'>إنشاء جداول الحضور أولاً</a></p>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
