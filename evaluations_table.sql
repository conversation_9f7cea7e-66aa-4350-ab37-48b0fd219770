-- جدول تقييمات المنتسبين
CREATE TABLE IF NOT EXISTS evaluations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    month DATE NOT NULL,
    tasks_completed INT DEFAULT 0,
    achievements_count INT DEFAULT 0,
    quality_score DECIMAL(3,2) DEFAULT 0.00,
    punctuality_score DECIMAL(3,2) DEFAULT 0.00,
    cooperation_score DECIMAL(3,2) DEFAULT 0.00,
    innovation_score DECIMAL(3,2) DEFAULT 0.00,
    total_score DECIMAL(5,2) DEFAULT 0.00,
    grade ENUM('ممتاز', 'جيد جداً', 'جيد', 'مقبول', 'ضعيف') DEFAULT 'مقبول',
    notes TEXT,
    evaluated_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON>OREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (evaluated_by) REFERENCES users(id),
    UNIQUE KEY unique_user_month (user_id, month)
);

-- إدراج تقييم تجريبي
INSERT IGNORE INTO evaluations (user_id, month, tasks_completed, achievements_count, quality_score, punctuality_score, cooperation_score, innovation_score, total_score, grade, notes, evaluated_by) VALUES 
(2, DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01'), 15, 8, 4.50, 4.20, 4.80, 4.00, 17.50, 'جيد جداً', 'منتسب متميز في الأداء والالتزام', 1);
