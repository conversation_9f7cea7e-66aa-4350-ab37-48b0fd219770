<?php
// ملف اختبار الاتصال بقاعدة البيانات
require_once 'config/database.php';

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

try {
    $conn = getDBConnection();
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح!</p>";
    
    // اختبار جدول المستخدمين
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $users_count = $stmt->fetch()['count'];
    echo "<p>عدد المستخدمين: $users_count</p>";
    
    // اختبار جدول المهام
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks");
    $stmt->execute();
    $tasks_count = $stmt->fetch()['count'];
    echo "<p>عدد المهام: $tasks_count</p>";
    
    // اختبار جدول الإنجازات
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM achievements");
    $stmt->execute();
    $achievements_count = $stmt->fetch()['count'];
    echo "<p>عدد الإنجازات: $achievements_count</p>";
    
    echo "<p style='color: green;'>✅ جميع الجداول تعمل بشكل صحيح!</p>";
    echo "<p><a href='index.php'>الانتقال إلى النظام</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الاتصال: " . $e->getMessage() . "</p>";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل XAMPP (Apache + MySQL)</li>";
    echo "<li>إنشاء قاعدة البيانات control_internet_db</li>";
    echo "<li>تنفيذ ملف database.sql</li>";
    echo "</ul>";
}
?>
