<?php
// ملف تشخيص الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>تشخيص النظام</h2>";

try {
    echo "<p>1. اختبار تضمين الملفات...</p>";
    require_once 'config/database.php';
    echo "<p style='color: green;'>✅ تم تحميل ملف قاعدة البيانات</p>";
    
    require_once 'includes/auth.php';
    echo "<p style='color: green;'>✅ تم تحميل ملف المصادقة</p>";
    
    echo "<p>2. اختبار الاتصال بقاعدة البيانات...</p>";
    $conn = getDBConnection();
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات</p>";
    
    echo "<p>3. اختبار الاستعلامات...</p>";
    
    // اختبار استعلام بسيط
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $result = $stmt->fetch();
    echo "<p style='color: green;'>✅ عدد المستخدمين: " . $result['count'] . "</p>";
    
    // اختبار استعلام التاريخ
    $today = date('Y-m-d');
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE date = ?");
    $stmt->execute([$today]);
    $result = $stmt->fetch();
    echo "<p style='color: green;'>✅ مهام اليوم: " . $result['count'] . "</p>";
    
    // اختبار استعلام المهام الحديثة
    $week_ago = date('Y-m-d', strtotime('-7 days'));
    $stmt = $conn->prepare("
        SELECT t.*, u.full_name 
        FROM tasks t 
        JOIN users u ON t.user_id = u.id 
        WHERE t.date >= ?
        ORDER BY t.created_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$week_ago]);
    $recent_tasks = $stmt->fetchAll();
    echo "<p style='color: green;'>✅ المهام الحديثة: " . count($recent_tasks) . "</p>";
    
    echo "<p style='color: green; font-weight: bold;'>🎉 جميع الاختبارات نجحت!</p>";
    echo "<p><a href='login.php'>الانتقال إلى صفحة تسجيل الدخول</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<p style='color: red;'>الملف: " . $e->getFile() . "</p>";
    echo "<p style='color: red;'>السطر: " . $e->getLine() . "</p>";
    
    echo "<h3>تأكد من:</h3>";
    echo "<ul>";
    echo "<li>تشغيل XAMPP (Apache + MySQL)</li>";
    echo "<li>إنشاء قاعدة البيانات control_internet_db</li>";
    echo "<li>تنفيذ ملف database.sql</li>";
    echo "<li>وجود جميع الملفات في المجلد الصحيح</li>";
    echo "</ul>";
}
?>
