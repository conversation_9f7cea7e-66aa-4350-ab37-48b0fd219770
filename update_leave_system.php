<?php
// تحديث نظام الإجازات المتطور
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تحديث نظام الإجازات المتطور</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }";
echo ".container { background: rgba(255,255,255,0.95); color: #333; padding: 40px; border-radius: 15px; box-shadow: 0 0 30px rgba(0,0,0,0.3); max-width: 900px; margin: 0 auto; }";
echo ".success { color: green; padding: 10px; background: #e8f5e8; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: red; padding: 10px; background: #ffe6e6; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: blue; padding: 10px; background: #e8f4fd; border-radius: 5px; margin: 10px 0; }";
echo ".warning { color: orange; padding: 10px; background: #fff3cd; border-radius: 5px; margin: 10px 0; }";
echo ".btn { background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin: 8px; display: inline-block; font-weight: bold; }";
echo ".btn-success { background: #28a745; }";
echo ".feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }";
echo ".feature-card { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 10px; border-left: 5px solid #667eea; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1 style='text-align: center; color: #667eea; margin-bottom: 30px;'>🏖️ تحديث نظام الإجازات المتطور</h1>";

try {
    $conn = getDBConnection();
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // 1. تحديث جدول أنواع الإجازات
    echo "<h3>📋 تحديث أنواع الإجازات...</h3>";
    
    // إضافة حقول جديدة لجدول أنواع الإجازات
    $new_columns = [
        "is_deductible BOOLEAN DEFAULT TRUE COMMENT 'هل تستقطع من الرصيد'",
        "is_unlimited BOOLEAN DEFAULT FALSE COMMENT 'غير محدودة'",
        "requires_approval BOOLEAN DEFAULT TRUE COMMENT 'تحتاج موافقة'",
        "can_be_hourly BOOLEAN DEFAULT FALSE COMMENT 'يمكن أخذها بالساعة'",
        "priority_order INT DEFAULT 1 COMMENT 'ترتيب الأولوية'",
        "color_code VARCHAR(7) DEFAULT '#007bff' COMMENT 'لون التمييز'",
        "icon VARCHAR(50) DEFAULT 'fas fa-calendar' COMMENT 'أيقونة'"
    ];
    
    foreach ($new_columns as $column_sql) {
        try {
            $conn->exec("ALTER TABLE leave_types ADD COLUMN $column_sql");
            echo "<div class='success'>✅ تم إضافة حقل جديد</div>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<div class='info'>ℹ️ الحقل موجود بالفعل</div>";
            } else {
                echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    // 2. تحديث جدول طلبات الإجازات
    echo "<h3>📝 تحديث جدول طلبات الإجازات...</h3>";
    
    $leave_request_columns = [
        "ADD COLUMN is_hourly BOOLEAN DEFAULT FALSE COMMENT 'إجازة بالساعة'",
        "ADD COLUMN hours_count DECIMAL(4,2) DEFAULT 0 COMMENT 'عدد الساعات'",
        "ADD COLUMN start_time TIME NULL COMMENT 'وقت البداية'",
        "ADD COLUMN end_time TIME NULL COMMENT 'وقت النهاية'",
        "ADD COLUMN emergency_type VARCHAR(100) NULL COMMENT 'نوع الحالة الطارئة'",
        "ADD COLUMN supporting_documents TEXT NULL COMMENT 'المستندات المرفقة'",
        "ADD COLUMN priority ENUM('عادي', 'عاجل', 'طارئ') DEFAULT 'عادي' COMMENT 'الأولوية'"
    ];
    
    foreach ($leave_request_columns as $column_sql) {
        try {
            $conn->exec("ALTER TABLE leave_requests $column_sql");
            echo "<div class='success'>✅ تم تحديث جدول طلبات الإجازات</div>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<div class='info'>ℹ️ الحقل موجود بالفعل</div>";
            } else {
                echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    // 3. تحديث جدول رصيد الإجازات
    echo "<h3>💰 تحديث جدول رصيد الإجازات...</h3>";
    
    $balance_columns = [
        "ADD COLUMN used_hours DECIMAL(6,2) DEFAULT 0 COMMENT 'الساعات المستخدمة'",
        "ADD COLUMN remaining_hours DECIMAL(6,2) DEFAULT 0 COMMENT 'الساعات المتبقية'",
        "ADD COLUMN last_reset_date DATE NULL COMMENT 'تاريخ آخر إعادة تعيين'"
    ];
    
    foreach ($balance_columns as $column_sql) {
        try {
            $conn->exec("ALTER TABLE annual_leave_balance $column_sql");
            echo "<div class='success'>✅ تم تحديث جدول رصيد الإجازات</div>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<div class='info'>ℹ️ الحقل موجود بالفعل</div>";
            } else {
                echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    // 4. حذف أنواع الإجازات القديمة وإضافة الجديدة
    echo "<h3>🔄 تحديث أنواع الإجازات...</h3>";
    
    // حذف الأنواع القديمة
    $conn->exec("DELETE FROM leave_types");
    echo "<div class='info'>ℹ️ تم حذف أنواع الإجازات القديمة</div>";
    
    // إضافة أنواع الإجازات الجديدة
    $new_leave_types = [
        [
            'name' => 'إجازة سنوية',
            'max_days_per_year' => 30,
            'description' => 'الإجازة السنوية العادية - 30 يوم سنوياً',
            'is_deductible' => 1,
            'is_unlimited' => 0,
            'requires_approval' => 1,
            'can_be_hourly' => 0,
            'priority_order' => 1,
            'color_code' => '#28a745',
            'icon' => 'fas fa-calendar-alt'
        ],
        [
            'name' => 'إجازة مرضية',
            'max_days_per_year' => 0,
            'description' => 'إجازة للحالات المرضية - غير محدودة',
            'is_deductible' => 0,
            'is_unlimited' => 1,
            'requires_approval' => 1,
            'can_be_hourly' => 0,
            'priority_order' => 2,
            'color_code' => '#dc3545',
            'icon' => 'fas fa-user-injured'
        ],
        [
            'name' => 'حالة وفاة',
            'max_days_per_year' => 0,
            'description' => 'إجازة حالة وفاة - غير محدودة',
            'is_deductible' => 0,
            'is_unlimited' => 1,
            'requires_approval' => 1,
            'can_be_hourly' => 0,
            'priority_order' => 3,
            'color_code' => '#6c757d',
            'icon' => 'fas fa-heart-broken'
        ],
        [
            'name' => 'إيفاد',
            'max_days_per_year' => 0,
            'description' => 'إيفاد رسمي للعمل - غير محدود',
            'is_deductible' => 0,
            'is_unlimited' => 1,
            'requires_approval' => 1,
            'can_be_hourly' => 0,
            'priority_order' => 4,
            'color_code' => '#17a2b8',
            'icon' => 'fas fa-briefcase'
        ],
        [
            'name' => 'ساعة طارئة',
            'max_days_per_year' => 0,
            'description' => 'ساعة زمنية للحالات الطارئة',
            'is_deductible' => 0,
            'is_unlimited' => 1,
            'requires_approval' => 1,
            'can_be_hourly' => 1,
            'priority_order' => 5,
            'color_code' => '#ffc107',
            'icon' => 'fas fa-clock'
        ],
        [
            'name' => 'إجازة أمومة',
            'max_days_per_year' => 90,
            'description' => 'إجازة الأمومة للموظفات - 90 يوم',
            'is_deductible' => 0,
            'is_unlimited' => 0,
            'requires_approval' => 1,
            'can_be_hourly' => 0,
            'priority_order' => 6,
            'color_code' => '#e83e8c',
            'icon' => 'fas fa-baby'
        ],
        [
            'name' => 'إجازة أبوة',
            'max_days_per_year' => 3,
            'description' => 'إجازة الأبوة للموظفين - 3 أيام',
            'is_deductible' => 0,
            'is_unlimited' => 0,
            'requires_approval' => 1,
            'can_be_hourly' => 0,
            'priority_order' => 7,
            'color_code' => '#6f42c1',
            'icon' => 'fas fa-male'
        ]
    ];
    
    $stmt = $conn->prepare("
        INSERT INTO leave_types (
            name, max_days_per_year, description, is_deductible, is_unlimited, 
            requires_approval, can_be_hourly, priority_order, color_code, icon
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    foreach ($new_leave_types as $type) {
        $stmt->execute([
            $type['name'], $type['max_days_per_year'], $type['description'],
            $type['is_deductible'], $type['is_unlimited'], $type['requires_approval'],
            $type['can_be_hourly'], $type['priority_order'], $type['color_code'], $type['icon']
        ]);
        echo "<div class='success'>✅ تم إضافة نوع إجازة: " . $type['name'] . "</div>";
    }
    
    // 5. إنشاء جدول سجل استخدام الساعات
    echo "<h3>⏰ إنشاء جدول سجل الساعات...</h3>";
    
    $hours_log_sql = "
    CREATE TABLE IF NOT EXISTS hourly_leave_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        leave_request_id INT NOT NULL,
        date DATE NOT NULL,
        start_time TIME NOT NULL,
        end_time TIME NOT NULL,
        hours_used DECIMAL(4,2) NOT NULL,
        reason TEXT,
        approved_by INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (leave_request_id) REFERENCES leave_requests(id) ON DELETE CASCADE,
        FOREIGN KEY (approved_by) REFERENCES users(id),
        INDEX idx_user_date (user_id, date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    try {
        $conn->exec($hours_log_sql);
        echo "<div class='success'>✅ تم إنشاء جدول سجل الساعات</div>";
    } catch (PDOException $e) {
        echo "<div class='info'>ℹ️ جدول سجل الساعات موجود بالفعل</div>";
    }
    
    // 6. إنشاء فهارس للأداء
    echo "<h3>🔍 إنشاء فهارس الأداء...</h3>";
    
    $indexes = [
        "CREATE INDEX idx_leave_types_priority ON leave_types(priority_order)",
        "CREATE INDEX idx_leave_requests_priority ON leave_requests(priority)",
        "CREATE INDEX idx_leave_requests_hourly ON leave_requests(is_hourly)",
        "CREATE INDEX idx_annual_balance_year ON annual_leave_balance(year, user_id)"
    ];
    
    foreach ($indexes as $index_sql) {
        try {
            $conn->exec($index_sql);
            echo "<div class='success'>✅ تم إنشاء فهرس</div>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<div class='info'>ℹ️ الفهرس موجود بالفعل</div>";
            }
        }
    }
    
    // 7. إعادة تهيئة أرصدة الإجازات للمنتسبين
    echo "<h3>🔄 إعادة تهيئة أرصدة الإجازات...</h3>";
    
    // حذف الأرصدة القديمة
    $conn->exec("DELETE FROM annual_leave_balance");
    
    // جلب المنتسبين
    $stmt = $conn->prepare("SELECT id FROM users WHERE role = 'user'");
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    // جلب أنواع الإجازات الجديدة
    $stmt = $conn->prepare("SELECT id, max_days_per_year, is_unlimited FROM leave_types");
    $stmt->execute();
    $leave_types = $stmt->fetchAll();
    
    $current_year = date('Y');
    $balance_stmt = $conn->prepare("
        INSERT INTO annual_leave_balance (
            user_id, year, leave_type_id, total_days, used_days, remaining_days, last_reset_date
        ) VALUES (?, ?, ?, ?, 0, ?, ?)
    ");
    
    foreach ($users as $user) {
        foreach ($leave_types as $type) {
            $total_days = $type['is_unlimited'] ? 999 : $type['max_days_per_year'];
            $balance_stmt->execute([
                $user['id'], $current_year, $type['id'], 
                $total_days, $total_days, $current_year . '-01-01'
            ]);
        }
    }
    
    echo "<div class='success'>✅ تم إعادة تهيئة أرصدة الإجازات لجميع المنتسبين</div>";
    
    // عرض ملخص التحديث
    echo "<div style='background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; border-radius: 15px; margin: 40px 0; text-align: center;'>";
    echo "<h2>🎉 تم تحديث نظام الإجازات بنجاح!</h2>";
    echo "</div>";
    
    // عرض أنواع الإجازات الجديدة
    echo "<div style='background: #e8f4fd; padding: 20px; border-radius: 10px; margin: 30px 0;'>";
    echo "<h3 style='color: #0066cc;'>📋 أنواع الإجازات الجديدة:</h3>";
    
    echo "<div class='feature-grid'>";
    
    foreach ($new_leave_types as $type) {
        echo "<div class='feature-card' style='border-left-color: " . $type['color_code'] . ";'>";
        echo "<h4 style='color: " . $type['color_code'] . ";'>";
        echo "<i class='" . $type['icon'] . " me-2'></i>" . $type['name'];
        echo "</h4>";
        echo "<p><strong>الوصف:</strong> " . $type['description'] . "</p>";
        echo "<ul>";
        echo "<li><strong>الحد الأقصى:</strong> " . ($type['is_unlimited'] ? 'غير محدود' : $type['max_days_per_year'] . ' يوم') . "</li>";
        echo "<li><strong>يستقطع من الرصيد:</strong> " . ($type['is_deductible'] ? 'نعم' : 'لا') . "</li>";
        echo "<li><strong>يمكن بالساعة:</strong> " . ($type['can_be_hourly'] ? 'نعم' : 'لا') . "</li>";
        echo "<li><strong>الأولوية:</strong> " . $type['priority_order'] . "</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
    
    // المميزات الجديدة
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 30px 0;'>";
    echo "<h3 style='color: #856404;'>🎯 المميزات الجديدة:</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>إجازة سنوية:</strong> 30 يوم تستقطع من الرصيد</li>";
    echo "<li>✅ <strong>إجازة مرضية:</strong> غير محدودة ولا تستقطع من الرصيد</li>";
    echo "<li>✅ <strong>حالة وفاة:</strong> غير محدودة ولا تستقطع من الرصيد</li>";
    echo "<li>✅ <strong>إيفاد:</strong> غير محدود ولا يستقطع من الرصيد</li>";
    echo "<li>✅ <strong>ساعة طارئة:</strong> بالساعة للحالات الطارئة</li>";
    echo "<li>✅ <strong>ترتيب حسب الأولوية:</strong> نظام ترتيب ذكي</li>";
    echo "<li>✅ <strong>ألوان مميزة:</strong> لكل نوع إجازة لون خاص</li>";
    echo "<li>✅ <strong>أيقونات تعبيرية:</strong> لسهولة التمييز</li>";
    echo "</ul>";
    echo "</div>";
    
    // روابط سريعة
    echo "<div style='text-align: center; margin: 40px 0;'>";
    echo "<h3>🚀 ابدأ الاستخدام:</h3>";
    echo "<a href='leave_requests.php' class='btn btn-success'>📝 طلبات الإجازات المحدثة</a>";
    echo "<a href='leave_balance.php' class='btn btn-success'>💰 رصيد الإجازات الجديد</a>";
    echo "<a href='attendance.php' class='btn btn-success'>📅 إدارة الحضور</a>";
    echo "<a href='dashboard.php' class='btn'>🏠 لوحة التحكم</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في التحديث: " . $e->getMessage() . "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
