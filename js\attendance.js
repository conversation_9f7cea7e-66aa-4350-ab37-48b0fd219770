// JavaScript لتحسين تجربة المستخدم في صفحات الحضور والإجازات

// دالة تأكيد الحذف
function confirmDelete(message) {
    return confirm(message || 'هل أنت متأكد من هذا الإجراء؟');
}

// دالة تبديل حقول الوقت حسب حالة الحضور
function toggleTimeFields(selectElement, userId) {
    const timeFields = document.querySelectorAll('.time-field-' + userId);
    const isPresent = selectElement.value === 'حاضر';
    
    timeFields.forEach(field => {
        field.disabled = !isPresent;
        if (!isPresent) {
            field.value = '';
        }
    });
}

// دالة تحديد جميع المنتسبين كحاضرين
function markAllPresent() {
    const statusSelects = document.querySelectorAll('select[name*="[status]"]');
    statusSelects.forEach(select => {
        select.value = 'حاضر';
        const userId = select.name.match(/\[(\d+)\]/)[1];
        toggleTimeFields(select, userId);
    });
}

// دالة تعيين الأوقات الافتراضية
function setDefaultTimes() {
    const checkInFields = document.querySelectorAll('input[name*="[check_in]"]');
    const checkOutFields = document.querySelectorAll('input[name*="[check_out]"]');
    
    checkInFields.forEach(field => {
        if (!field.disabled && !field.value) {
            field.value = '08:00';
        }
    });
    
    checkOutFields.forEach(field => {
        if (!field.disabled && !field.value) {
            field.value = '16:00';
        }
    });
}

// دالة حساب عدد أيام الإجازة
function calculateDays() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const timeDiff = end.getTime() - start.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
        
        if (daysDiff > 0) {
            document.getElementById('days_display').value = daysDiff + ' يوم';
        } else {
            document.getElementById('days_display').value = 'تاريخ غير صحيح';
        }
    }
}

// دالة عرض نافذة رفض الطلب
function showRejectModal(requestId) {
    document.getElementById('reject_request_id').value = requestId;
    document.getElementById('rejection_reason').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
    modal.show();
}

// دالة عرض نافذة تهيئة الأرصدة
function showInitializeModal() {
    const modal = new bootstrap.Modal(document.getElementById('initializeModal'));
    modal.show();
}

// دالة عرض نافذة تحديث الرصيد
function showUpdateModal(balanceId, totalDays, userName, leaveType) {
    document.getElementById('update_balance_id').value = balanceId;
    document.getElementById('total_days').value = totalDays;
    document.getElementById('update_user_name').textContent = userName;
    document.getElementById('update_leave_type').textContent = leaveType;
    
    const modal = new bootstrap.Modal(document.getElementById('updateModal'));
    modal.show();
}

// دالة تحديث حالة المهمة
function updateStatus(taskId, status) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';
    
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'update_status';
    
    const taskIdInput = document.createElement('input');
    taskIdInput.type = 'hidden';
    taskIdInput.name = 'task_id';
    taskIdInput.value = taskId;
    
    const statusInput = document.createElement('input');
    statusInput.type = 'hidden';
    statusInput.name = 'status';
    statusInput.value = status;
    
    form.appendChild(actionInput);
    form.appendChild(taskIdInput);
    form.appendChild(statusInput);
    
    document.body.appendChild(form);
    form.submit();
}

// دالة إعادة تعيين كلمة المرور
function resetPassword(userId, userName) {
    document.getElementById('reset_user_id').value = userId;
    document.getElementById('reset_user_name').textContent = userName;
    document.getElementById('new_password').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
}

// تطبيق الإعدادات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تطبيق حالة الحقول للحضور
    const statusSelects = document.querySelectorAll('select[name*="[status]"]');
    statusSelects.forEach(select => {
        const userId = select.name.match(/\[(\d+)\]/);
        if (userId) {
            toggleTimeFields(select, userId[1]);
        }
    });
    
    // إضافة مستمعات الأحداث لحقول التاريخ في طلبات الإجازة
    const startDateField = document.getElementById('start_date');
    const endDateField = document.getElementById('end_date');
    
    if (startDateField && endDateField) {
        startDateField.addEventListener('change', calculateDays);
        endDateField.addEventListener('change', calculateDays);
    }
    
    // تحسين تجربة المستخدم للجداول الطويلة
    const tables = document.querySelectorAll('.table-responsive');
    tables.forEach(table => {
        // إضافة تأثيرات التمرير
        table.addEventListener('scroll', function() {
            if (this.scrollLeft > 0) {
                this.classList.add('scrolled');
            } else {
                this.classList.remove('scrolled');
            }
        });
    });
    
    // تحسين النماذج
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
                
                // إعادة تفعيل الزر بعد 3 ثوان في حالة عدم إعادة التوجيه
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = submitBtn.getAttribute('data-original-text') || 'حفظ';
                }, 3000);
            }
        });
    });
});

// دالة طباعة محسنة
function printPage() {
    // إخفاء العناصر غير المرغوب فيها في الطباعة
    const noPrintElements = document.querySelectorAll('.no-print, .btn-group, .dropdown');
    noPrintElements.forEach(element => {
        element.style.display = 'none';
    });
    
    // طباعة الصفحة
    window.print();
    
    // إعادة إظهار العناصر بعد الطباعة
    setTimeout(() => {
        noPrintElements.forEach(element => {
            element.style.display = '';
        });
    }, 1000);
}

// دالة تصدير البيانات إلى CSV
function exportToCSV(tableId, filename) {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    let csv = [];
    const rows = table.querySelectorAll('tr');
    
    for (let i = 0; i < rows.length; i++) {
        const row = [];
        const cols = rows[i].querySelectorAll('td, th');
        
        for (let j = 0; j < cols.length; j++) {
            let text = cols[j].innerText;
            text = text.replace(/"/g, '""'); // escape double quotes
            row.push('"' + text + '"');
        }
        
        csv.push(row.join(','));
    }
    
    // تحميل الملف
    const csvFile = new Blob([csv.join('\n')], { type: 'text/csv' });
    const downloadLink = document.createElement('a');
    downloadLink.download = filename || 'data.csv';
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = 'none';
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}

// دالة البحث السريع في الجداول
function quickSearch(inputId, tableId) {
    const input = document.getElementById(inputId);
    const table = document.getElementById(tableId);
    
    if (!input || !table) return;
    
    input.addEventListener('keyup', function() {
        const filter = this.value.toLowerCase();
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(filter) ? '' : 'none';
        });
    });
}
