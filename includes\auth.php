<?php
session_start();

// التحقق من تسجيل الدخول
function checkLogin() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit();
    }
}

// التحقق من صلاحيات المدير
function checkAdmin() {
    checkLogin();
    if ($_SESSION['role'] !== 'admin') {
        $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
        header('Location: user_dashboard.php');
        exit();
    }
}

// الحصول على بيانات المستخدم الحالي
function getCurrentUser() {
    if (!isset($_SESSION['user_id'])) {
        return null;
    }
    
    return [
        'id' => $_SESSION['user_id'],
        'username' => $_SESSION['username'],
        'role' => $_SESSION['role'],
        'full_name' => $_SESSION['full_name']
    ];
}

// التحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// تشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}
?>