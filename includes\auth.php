<?php
session_start();

// التحقق من تسجيل الدخول
function checkLogin() {
    if (!isset($_SESSION['user_id'])) {
        header('Location: login.php');
        exit();
    }
}

// التحقق من صلاحيات المدير
function checkAdmin() {
    checkLogin();
    if ($_SESSION['role'] !== 'admin') {
        header('Location: dashboard.php');
        exit();
    }
}

// تسجيل الخروج
function logout() {
    session_destroy();
    header('Location: login.php');
    exit();
}

// الحصول على معلومات المستخدم الحالي
function getCurrentUser() {
    return [
        'id' => $_SESSION['user_id'] ?? null,
        'username' => $_SESSION['username'] ?? null,
        'role' => $_SESSION['role'] ?? null,
        'full_name' => $_SESSION['full_name'] ?? null
    ];
}

// التحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// تشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// التحقق من صلاحيات المدير
function checkAdmin() {
    checkLogin();
    if ($_SESSION['role'] !== 'admin') {
        $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
        header('Location: user_dashboard.php');
        exit();
    }
}
?>
