<?php
// فحص سريع لحالة قاعدة البيانات
require_once 'config/database.php';

echo "<h2>🔍 فحص قاعدة البيانات</h2>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .warning{color:orange;}</style>";

try {
    $conn = getDBConnection();
    echo "<p class='success'>✅ الاتصال بقاعدة البيانات: نجح</p>";
    
    // فحص الجداول المطلوبة
    $required_tables = ['users', 'tasks', 'achievements', 'evaluations'];
    
    foreach ($required_tables as $table) {
        try {
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM $table");
            $stmt->execute();
            $count = $stmt->fetch()['count'];
            echo "<p class='success'>✅ جدول $table: موجود ($count سجل)</p>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ جدول $table: غير موجود</p>";
            
            if ($table === 'evaluations') {
                echo "<p class='warning'>⚠️ يمكنك إنشاء جدول التقييمات من <a href='update_database.php'>هنا</a></p>";
            }
        }
    }
    
    echo "<hr>";
    echo "<h3>🔗 روابط مفيدة:</h3>";
    echo "<p><a href='install.php'>🔧 إعادة تثبيت قاعدة البيانات</a></p>";
    echo "<p><a href='update_database.php'>🔄 تحديث قاعدة البيانات (إضافة جدول التقييمات)</a></p>";
    echo "<p><a href='evaluations.php'>⭐ تقييم المنتسبين</a></p>";
    echo "<p><a href='dashboard.php'>🏠 لوحة التحكم</a></p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<p>تأكد من تشغيل XAMPP وإنشاء قاعدة البيانات</p>";
}
?>
