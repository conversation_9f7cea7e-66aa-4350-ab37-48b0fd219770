<?php
// تحديث جدول التقييمات لإضافة حقول الإحصائيات التفصيلية
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تحديث جدول التقييمات</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }";
echo ".container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); max-width: 800px; margin: 0 auto; }";
echo ".success { color: green; padding: 10px; background: #e8f5e8; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: red; padding: 10px; background: #ffe6e6; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: blue; padding: 10px; background: #e8f4fd; border-radius: 5px; margin: 10px 0; }";
echo ".btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔄 تحديث جدول التقييمات</h1>";

try {
    $conn = getDBConnection();
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // إضافة الحقول الجديدة لجدول التقييمات
    echo "<h3>📊 إضافة حقول الإحصائيات التفصيلية...</h3>";
    
    $new_columns = [
        "ADD COLUMN task_completion_rate DECIMAL(5,2) DEFAULT 0 COMMENT 'نسبة إنجاز المهام'",
        "ADD COLUMN attendance_rate DECIMAL(5,2) DEFAULT 0 COMMENT 'نسبة الحضور'", 
        "ADD COLUMN total_tasks INT DEFAULT 0 COMMENT 'إجمالي المهام'",
        "ADD COLUMN pending_tasks INT DEFAULT 0 COMMENT 'المهام المعلقة'",
        "ADD COLUMN cancelled_tasks INT DEFAULT 0 COMMENT 'المهام الملغية'",
        "ADD COLUMN present_days INT DEFAULT 0 COMMENT 'أيام الحضور'",
        "ADD COLUMN absent_days INT DEFAULT 0 COMMENT 'أيام الغياب'",
        "ADD COLUMN leave_days INT DEFAULT 0 COMMENT 'أيام الإجازة'",
        "ADD COLUMN mission_days INT DEFAULT 0 COMMENT 'أيام الإيفاد'",
        "ADD COLUMN sick_days INT DEFAULT 0 COMMENT 'أيام المرض'",
        "ADD COLUMN leave_requests_count INT DEFAULT 0 COMMENT 'عدد طلبات الإجازة'",
        "ADD COLUMN approved_leave_days INT DEFAULT 0 COMMENT 'أيام الإجازة المعتمدة'",
        "ADD COLUMN rejected_requests INT DEFAULT 0 COMMENT 'الطلبات المرفوضة'",
        "ADD COLUMN performance_notes TEXT COMMENT 'ملاحظات الأداء التفصيلية'"
    ];
    
    foreach ($new_columns as $column_sql) {
        try {
            $conn->exec("ALTER TABLE evaluations $column_sql");
            echo "<div class='success'>✅ تم إضافة حقل جديد</div>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<div class='info'>ℹ️ الحقل موجود بالفعل</div>";
            } else {
                echo "<div class='error'>❌ خطأ في إضافة حقل: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    // إنشاء فهارس للأداء
    echo "<h3>🔍 إضافة فهارس للأداء...</h3>";
    
    $indexes = [
        "CREATE INDEX idx_evaluations_month ON evaluations(month)",
        "CREATE INDEX idx_evaluations_user_month ON evaluations(user_id, month)",
        "CREATE INDEX idx_evaluations_grade ON evaluations(grade)",
        "CREATE INDEX idx_evaluations_total_score ON evaluations(total_score)"
    ];
    
    foreach ($indexes as $index_sql) {
        try {
            $conn->exec($index_sql);
            echo "<div class='success'>✅ تم إضافة فهرس</div>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<div class='info'>ℹ️ الفهرس موجود بالفعل</div>";
            } else {
                echo "<div class='error'>❌ خطأ في إضافة فهرس: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    // التحقق من الجدول المحدث
    echo "<h3>🔍 التحقق من الجدول المحدث...</h3>";
    
    $stmt = $conn->prepare("DESCRIBE evaluations");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<div class='info'>";
    echo "<h4>حقول جدول التقييمات:</h4>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li><strong>" . $column['Field'] . "</strong> - " . $column['Type'];
        if ($column['Comment']) {
            echo " (" . $column['Comment'] . ")";
        }
        echo "</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    // إنشاء view للتقارير المتقدمة
    echo "<h3>📈 إنشاء View للتقارير المتقدمة...</h3>";
    
    $view_sql = "
    CREATE OR REPLACE VIEW evaluation_summary AS
    SELECT 
        e.*,
        u.full_name,
        u.username,
        YEAR(e.month) as evaluation_year,
        MONTH(e.month) as evaluation_month,
        CASE 
            WHEN e.total_score >= 18 THEN 'ممتاز'
            WHEN e.total_score >= 15 THEN 'جيد جداً'
            WHEN e.total_score >= 12 THEN 'جيد'
            WHEN e.total_score >= 10 THEN 'مقبول'
            ELSE 'ضعيف'
        END as calculated_grade,
        (e.quality_score + e.punctuality_score + e.cooperation_score + e.innovation_score) as manual_total,
        e.attendance_rate,
        e.task_completion_rate,
        (e.present_days + e.leave_days + e.mission_days) as working_days,
        e.absent_days + e.sick_days as non_working_days
    FROM evaluations e
    JOIN users u ON e.user_id = u.id
    WHERE u.role = 'user'
    ";
    
    try {
        $conn->exec($view_sql);
        echo "<div class='success'>✅ تم إنشاء View للتقارير المتقدمة</div>";
    } catch (PDOException $e) {
        echo "<div class='error'>❌ خطأ في إنشاء View: " . $e->getMessage() . "</div>";
    }
    
    echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 30px 0;'>";
    echo "<h2>🎉 تم التحديث بنجاح!</h2>";
    echo "<p><strong>الحقول الجديدة المضافة:</strong></p>";
    echo "<ul>";
    echo "<li>📊 <strong>نسبة إنجاز المهام:</strong> task_completion_rate</li>";
    echo "<li>📅 <strong>نسبة الحضور:</strong> attendance_rate</li>";
    echo "<li>📋 <strong>إحصائيات المهام:</strong> total_tasks, pending_tasks, cancelled_tasks</li>";
    echo "<li>🏢 <strong>إحصائيات الحضور:</strong> present_days, absent_days, leave_days, mission_days, sick_days</li>";
    echo "<li>📝 <strong>إحصائيات الإجازات:</strong> leave_requests_count, approved_leave_days, rejected_requests</li>";
    echo "<li>📄 <strong>ملاحظات الأداء:</strong> performance_notes</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #e8f4fd; padding: 20px; border-radius: 10px; margin: 30px 0;'>";
    echo "<h3>🎯 المميزات الجديدة:</h3>";
    echo "<ul>";
    echo "<li>ربط التقييم بنسبة إنجاز المهام</li>";
    echo "<li>ربط التقييم بنسبة الحضور والغياب</li>";
    echo "<li>ربط التقييم بعدد الإنجازات</li>";
    echo "<li>ربط التقييم بسلوك استخدام الإجازات</li>";
    echo "<li>حساب تلقائي للدرجات بناءً على الأداء الفعلي</li>";
    echo "<li>تقارير متقدمة مع إحصائيات شاملة</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<h3>🚀 ابدأ الآن:</h3>";
    echo "<a href='evaluations.php' class='btn'>⭐ نظام التقييم المحدث</a>";
    echo "<a href='evaluation_reports.php' class='btn'>📊 التقارير المتقدمة</a>";
    echo "<a href='dashboard.php' class='btn'>🏠 لوحة التحكم</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في التحديث: " . $e->getMessage() . "</div>";
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>💡 تأكد من:</h3>";
    echo "<ul>";
    echo "<li>تشغيل XAMPP (Apache + MySQL)</li>";
    echo "<li>وجود قاعدة البيانات control_internet_db</li>";
    echo "<li>وجود جدول evaluations</li>";
    echo "<li>صلاحيات التعديل في قاعدة البيانات</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
