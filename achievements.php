<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkLogin();

$user = getCurrentUser();
$conn = getDBConnection();
$action = $_GET['action'] ?? 'list';
$achievement_id = $_GET['id'] ?? null;

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $post_action = $_POST['action'] ?? '';
    
    if ($post_action === 'add') {
        $title = trim($_POST['title'] ?? '');
        $details = trim($_POST['details'] ?? '');
        $date = $_POST['date'] ?? date('Y-m-d');
        
        if (!empty($title)) {
            try {
                $stmt = $conn->prepare("INSERT INTO achievements (user_id, title, details, date) VALUES (?, ?, ?, ?)");
                $stmt->execute([$user['id'], $title, $details, $date]);
                $_SESSION['success'] = 'تم إضافة الإنجاز بنجاح';
                header('Location: achievements.php');
                exit();
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء إضافة الإنجاز';
            }
        } else {
            $_SESSION['error'] = 'يرجى إدخال عنوان الإنجاز';
        }
    }
    
    elseif ($post_action === 'edit') {
        $id = $_POST['id'] ?? '';
        $title = trim($_POST['title'] ?? '');
        $details = trim($_POST['details'] ?? '');
        $date = $_POST['date'] ?? '';
        
        if (!empty($id) && !empty($title)) {
            try {
                // التحقق من ملكية الإنجاز أو صلاحيات المدير
                $where_condition = $user['role'] === 'admin' ? 'id = ?' : 'id = ? AND user_id = ?';
                $params = $user['role'] === 'admin' ? [$title, $details, $date, $id] : [$title, $details, $date, $id, $user['id']];
                
                $stmt = $conn->prepare("UPDATE achievements SET title = ?, details = ?, date = ? WHERE $where_condition");
                $stmt->execute($params);
                
                if ($stmt->rowCount() > 0) {
                    $_SESSION['success'] = 'تم تحديث الإنجاز بنجاح';
                } else {
                    $_SESSION['error'] = 'لم يتم العثور على الإنجاز أو ليس لديك صلاحية لتعديله';
                }
                header('Location: achievements.php');
                exit();
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء تحديث الإنجاز';
            }
        } else {
            $_SESSION['error'] = 'يرجى ملء جميع الحقول المطلوبة';
        }
    }
    
    elseif ($post_action === 'delete') {
        $id = $_POST['id'] ?? '';
        
        if (!empty($id)) {
            try {
                $where_condition = $user['role'] === 'admin' ? 'id = ?' : 'id = ? AND user_id = ?';
                $params = $user['role'] === 'admin' ? [$id] : [$id, $user['id']];
                
                $stmt = $conn->prepare("DELETE FROM achievements WHERE $where_condition");
                $stmt->execute($params);
                
                if ($stmt->rowCount() > 0) {
                    $_SESSION['success'] = 'تم حذف الإنجاز بنجاح';
                } else {
                    $_SESSION['error'] = 'لم يتم العثور على الإنجاز أو ليس لديك صلاحية لحذفه';
                }
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء حذف الإنجاز';
            }
        }
        header('Location: achievements.php');
        exit();
    }
}

// جلب الإنجازات
$where_condition = $user['role'] === 'admin' ? '' : 'WHERE a.user_id = ' . $user['id'];
$stmt = $conn->prepare("
    SELECT a.*, u.full_name 
    FROM achievements a 
    JOIN users u ON a.user_id = u.id 
    $where_condition
    ORDER BY a.date DESC, a.created_at DESC
");
$stmt->execute();
$achievements = $stmt->fetchAll();

// جلب إنجاز للتعديل
$edit_achievement = null;
if ($action === 'edit' && $achievement_id) {
    $where_condition = $user['role'] === 'admin' ? 'id = ?' : 'id = ? AND user_id = ?';
    $params = $user['role'] === 'admin' ? [$achievement_id] : [$achievement_id, $user['id']];
    
    $stmt = $conn->prepare("SELECT * FROM achievements WHERE $where_condition");
    $stmt->execute($params);
    $edit_achievement = $stmt->fetch();
    
    if (!$edit_achievement) {
        $_SESSION['error'] = 'لم يتم العثور على الإنجاز';
        header('Location: achievements.php');
        exit();
    }
}

$page_title = 'إدارة الإنجازات';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-trophy me-2"></i>
                إدارة الإنجازات
            </h1>
            <?php if ($action !== 'add' && $action !== 'edit'): ?>
                <a href="achievements.php?action=add" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>
                    إضافة إنجاز جديد
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php if ($action === 'add' || $action === 'edit'): ?>
<!-- نموذج إضافة/تعديل الإنجاز -->
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?> me-2"></i>
                <?php echo $action === 'add' ? 'إضافة إنجاز جديد' : 'تعديل الإنجاز'; ?>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="action" value="<?php echo $action; ?>">
                    <?php if ($action === 'edit'): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_achievement['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="title" class="form-label">عنوان الإنجاز *</label>
                            <input type="text" class="form-control" id="title" name="title" 
                                   value="<?php echo htmlspecialchars($edit_achievement['title'] ?? ''); ?>" 
                                   placeholder="مثال: إكمال صيانة 5 كاميرات" required>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="date" class="form-label">التاريخ *</label>
                            <input type="date" class="form-control" id="date" name="date" 
                                   value="<?php echo $edit_achievement['date'] ?? date('Y-m-d'); ?>" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="details" class="form-label">تفاصيل الإنجاز</label>
                        <textarea class="form-control" id="details" name="details" rows="5" 
                                  placeholder="اكتب تفاصيل الإنجاز هنا..."><?php echo htmlspecialchars($edit_achievement['details'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>
                            <?php echo $action === 'add' ? 'إضافة الإنجاز' : 'حفظ التغييرات'; ?>
                        </button>
                        <a href="achievements.php" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php else: ?>
<!-- قائمة الإنجازات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>
                    <i class="fas fa-list me-2"></i>
                    قائمة الإنجازات (<?php echo count($achievements); ?> إنجاز)
                </span>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="window.print()">
                        <i class="fas fa-print me-1"></i>
                        طباعة
                    </button>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($achievements)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد إنجازات مسجلة</h5>
                        <p class="text-muted">ابدأ بإضافة إنجاز جديد</p>
                        <a href="achievements.php?action=add" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>
                            إضافة إنجاز جديد
                        </a>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($achievements as $achievement): ?>
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div class="badge bg-success">
                                                <i class="fas fa-trophy me-1"></i>
                                                إنجاز
                                            </div>
                                            <small class="text-muted">
                                                <?php echo date('Y-m-d', strtotime($achievement['date'])); ?>
                                            </small>
                                        </div>
                                        
                                        <h5 class="card-title text-primary">
                                            <?php echo htmlspecialchars($achievement['title']); ?>
                                        </h5>
                                        
                                        <?php if (!empty($achievement['details'])): ?>
                                            <p class="card-text text-muted">
                                                <?php echo htmlspecialchars(substr($achievement['details'], 0, 100)); ?>
                                                <?php if (strlen($achievement['details']) > 100): ?>...<?php endif; ?>
                                            </p>
                                        <?php endif; ?>
                                        
                                        <?php if ($user['role'] === 'admin'): ?>
                                            <div class="mb-3">
                                                <small class="text-muted">
                                                    <i class="fas fa-user me-1"></i>
                                                    <?php echo htmlspecialchars($achievement['full_name']); ?>
                                                </small>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="d-flex gap-2">
                                            <a href="achievements.php?action=edit&id=<?php echo $achievement['id']; ?>" 
                                               class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit me-1"></i>
                                                تعديل
                                            </a>
                                            
                                            <form method="POST" style="display: inline;" 
                                                  onsubmit="return confirmDelete('هل أنت متأكد من حذف هذا الإنجاز؟')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="id" value="<?php echo $achievement['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash me-1"></i>
                                                    حذف
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
