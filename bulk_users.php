<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkAdmin(); // فقط المدير يمكنه الوصول

$user = getCurrentUser();
$conn = getDBConnection();

// معالجة إضافة المنتسبين بالجملة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['bulk_add'])) {
    $users_data = $_POST['users_data'] ?? '';
    $default_password = $_POST['default_password'] ?? 'password123';
    
    if (!empty($users_data)) {
        $lines = explode("\n", trim($users_data));
        $success_count = 0;
        $error_count = 0;
        $errors = [];
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;
            
            // تحليل السطر: الاسم الكامل | اسم المستخدم (اختياري)
            $parts = explode('|', $line);
            $full_name = trim($parts[0]);
            $username = isset($parts[1]) ? trim($parts[1]) : '';
            
            // إنشاء اسم مستخدم تلقائي إذا لم يتم توفيره
            if (empty($username)) {
                $username = strtolower(str_replace(' ', '.', $full_name));
                $username = preg_replace('/[^a-z0-9.]/', '', $username);
            }
            
            if (!empty($full_name) && !empty($username)) {
                try {
                    // التحقق من عدم وجود اسم المستخدم
                    $stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
                    $stmt->execute([$username]);
                    if ($stmt->fetch()) {
                        $errors[] = "اسم المستخدم '$username' موجود بالفعل";
                        $error_count++;
                    } else {
                        $hashed_password = hashPassword($default_password);
                        $stmt = $conn->prepare("INSERT INTO users (username, password, role, full_name) VALUES (?, ?, 'user', ?)");
                        $stmt->execute([$username, $hashed_password, $full_name]);
                        $success_count++;
                    }
                } catch (Exception $e) {
                    $errors[] = "خطأ في إضافة '$full_name': " . $e->getMessage();
                    $error_count++;
                }
            } else {
                $errors[] = "بيانات غير صحيحة في السطر: $line";
                $error_count++;
            }
        }
        
        if ($success_count > 0) {
            $_SESSION['success'] = "تم إضافة $success_count منتسب بنجاح";
        }
        if ($error_count > 0) {
            $_SESSION['error'] = "فشل في إضافة $error_count منتسب. " . implode(', ', array_slice($errors, 0, 3));
        }
        
        header('Location: users_management.php');
        exit();
    } else {
        $_SESSION['error'] = 'يرجى إدخال بيانات المنتسبين';
    }
}

$page_title = 'إضافة منتسبين بالجملة';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-users-cog me-2"></i>
                إضافة منتسبين بالجملة
            </h1>
            <a href="users_management.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                العودة لإدارة المنتسبين
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-upload me-2"></i>
                إضافة عدة منتسبين دفعة واحدة
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>تعليمات الاستخدام:</h6>
                    <ul class="mb-0">
                        <li>اكتب كل منتسب في سطر منفصل</li>
                        <li>الصيغة: <code>الاسم الكامل | اسم المستخدم</code></li>
                        <li>اسم المستخدم اختياري (سيتم إنشاؤه تلقائياً من الاسم)</li>
                        <li>مثال: <code>أحمد محمد علي | ahmed.ali</code></li>
                        <li>أو: <code>فاطمة حسن</code> (سيصبح اسم المستخدم: fatima.hasan)</li>
                    </ul>
                </div>
                
                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="users_data" class="form-label">بيانات المنتسبين *</label>
                        <textarea class="form-control" id="users_data" name="users_data" rows="10" 
                                  placeholder="أحمد محمد علي | ahmed.ali&#10;فاطمة حسن محمد | fatima.hassan&#10;علي أحمد سالم&#10;مريم خالد عبدالله | maryam.khalid" required></textarea>
                        <small class="text-muted">اكتب كل منتسب في سطر منفصل</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="default_password" class="form-label">كلمة المرور الافتراضية *</label>
                        <input type="text" class="form-control" id="default_password" name="default_password" 
                               value="password123" required>
                        <small class="text-muted">ستستخدم هذه كلمة المرور لجميع المنتسبين الجدد</small>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" name="bulk_add" class="btn btn-primary">
                            <i class="fas fa-users me-2"></i>
                            إضافة جميع المنتسبين
                        </button>
                        <a href="users_management.php" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-file-excel me-2"></i>
                نموذج Excel للاستيراد
            </div>
            <div class="card-body">
                <p>يمكنك إنشاء ملف Excel بالتنسيق التالي ثم نسخ البيانات:</p>
                
                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>الاسم الكامل</th>
                                <th>اسم المستخدم</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>أحمد محمد علي</td>
                                <td>ahmed.ali</td>
                            </tr>
                            <tr>
                                <td>فاطمة حسن محمد</td>
                                <td>fatima.hassan</td>
                            </tr>
                            <tr>
                                <td>علي أحمد سالم</td>
                                <td>ali.salem</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="alert alert-warning">
                    <small>
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <strong>ملاحظة:</strong> عند النسخ من Excel، تأكد من استخدام الرمز | (pipe) للفصل بين الاسم واسم المستخدم
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
