<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkLogin();

$user = getCurrentUser();
$conn = getDBConnection();

// التأكد من أن المستخدم منتسب وليس مدير
if ($user['role'] === 'admin') {
    header('Location: dashboard.php');
    exit();
}

// إحصائيات المنتسب
$today = date('Y-m-d');
$current_month = date('Y-m');

// عدد المهام اليوم
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE date = ? AND user_id = ?");
$stmt->execute([$today, $user['id']]);
$tasks_today = $stmt->fetch()['count'];

// عدد المهام المكتملة اليوم
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE date = ? AND status = 'منجز' AND user_id = ?");
$stmt->execute([$today, $user['id']]);
$completed_tasks = $stmt->fetch()['count'];

// عدد الإنجازات اليوم
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM achievements WHERE date = ? AND user_id = ?");
$stmt->execute([$today, $user['id']]);
$achievements_today = $stmt->fetch()['count'];

// إجمالي المهام هذا الشهر
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE DATE_FORMAT(date, '%Y-%m') = ? AND user_id = ?");
$stmt->execute([$current_month, $user['id']]);
$monthly_tasks = $stmt->fetch()['count'];

// المهام المكتملة هذا الشهر
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE DATE_FORMAT(date, '%Y-%m') = ? AND status = 'منجز' AND user_id = ?");
$stmt->execute([$current_month, $user['id']]);
$monthly_completed = $stmt->fetch()['count'];

// الإنجازات هذا الشهر
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM achievements WHERE DATE_FORMAT(date, '%Y-%m') = ? AND user_id = ?");
$stmt->execute([$current_month, $user['id']]);
$monthly_achievements = $stmt->fetch()['count'];

// المهام الحديثة (آخر 5)
$stmt = $conn->prepare("
    SELECT * FROM tasks 
    WHERE user_id = ? 
    ORDER BY created_at DESC 
    LIMIT 5
");
$stmt->execute([$user['id']]);
$recent_tasks = $stmt->fetchAll();

// الإنجازات الحديثة (آخر 5)
$stmt = $conn->prepare("
    SELECT * FROM achievements 
    WHERE user_id = ? 
    ORDER BY created_at DESC 
    LIMIT 5
");
$stmt->execute([$user['id']]);
$recent_achievements = $stmt->fetchAll();

// آخر تقييم
$stmt = $conn->prepare("
    SELECT e.*, admin.full_name as evaluated_by_name
    FROM evaluations e 
    JOIN users admin ON e.evaluated_by = admin.id
    WHERE e.user_id = ? 
    ORDER BY e.month DESC 
    LIMIT 1
");
$stmt->execute([$user['id']]);
$latest_evaluation = $stmt->fetch();

$page_title = 'لوحة التحكم - ' . $user['full_name'];
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-tachometer-alt me-2"></i>
                مرحباً <?php echo htmlspecialchars($user['full_name']); ?>
            </h1>
            <div class="text-muted">
                <i class="fas fa-calendar-day me-1"></i>
                <?php echo date('Y-m-d'); ?>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات اليوم -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-tasks fa-2x mb-2"></i>
                <h3><?php echo $tasks_today; ?></h3>
                <p class="mb-0">مهام اليوم</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h3><?php echo $completed_tasks; ?></h3>
                <p class="mb-0">مهام مكتملة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-trophy fa-2x mb-2"></i>
                <h3><?php echo $achievements_today; ?></h3>
                <p class="mb-0">إنجازات اليوم</p>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات الشهر -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-calendar-month me-2"></i>
                إحصائيات الشهر الحالي (<?php echo date('Y/m'); ?>)
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="border-end">
                            <h4 class="text-info"><?php echo $monthly_tasks; ?></h4>
                            <p class="text-muted mb-0">إجمالي المهام</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border-end">
                            <h4 class="text-success"><?php echo $monthly_completed; ?></h4>
                            <p class="text-muted mb-0">المهام المكتملة</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h4 class="text-warning"><?php echo $monthly_achievements; ?></h4>
                        <p class="text-muted mb-0">الإنجازات</p>
                    </div>
                </div>
                
                <?php if ($monthly_tasks > 0): ?>
                    <div class="mt-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>معدل الإنجاز:</span>
                            <span class="fw-bold"><?php echo number_format(($monthly_completed / $monthly_tasks) * 100, 1); ?>%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: <?php echo ($monthly_completed / $monthly_tasks) * 100; ?>%"></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- الأعمال السريعة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt me-2"></i>
                الأعمال السريعة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <a href="user_tasks.php?action=add" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مهمة جديدة
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="user_achievements.php?action=add" class="btn btn-success w-100">
                            <i class="fas fa-trophy me-2"></i>
                            إضافة إنجاز جديد
                        </a>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <a href="user_tasks.php" class="btn btn-info w-100">
                            <i class="fas fa-list me-2"></i>
                            عرض مهامي
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="user_achievements.php" class="btn btn-warning w-100">
                            <i class="fas fa-star me-2"></i>
                            عرض إنجازاتي
                        </a>
                    </div>
                </div>
                <div class="row">
                    <?php if ($latest_evaluation): ?>
                    <div class="col-md-4 mb-2">
                        <a href="user_evaluation.php" class="btn btn-secondary w-100">
                            <i class="fas fa-chart-line me-2"></i>
                            عرض تقييماتي
                        </a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="leave_requests.php" class="btn btn-info w-100">
                            <i class="fas fa-calendar-alt me-2"></i>
                            طلب إجازة
                        </a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="print_leave_details.php?user_id=<?php echo $user['id']; ?>" class="btn btn-success w-100">
                            <i class="fas fa-print me-2"></i>
                            طباعة تفاصيل الإجازات
                        </a>
                    </div>
                    <?php else: ?>
                    <div class="col-md-12 mb-2">
                        <a href="leave_requests.php" class="btn btn-info w-100">
                            <i class="fas fa-calendar-alt me-2"></i>
                            طلب إجازة
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- المهام الحديثة -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>
                    <i class="fas fa-tasks me-2"></i>
                    مهامي الحديثة
                </span>
                <a href="user_tasks.php" class="btn btn-sm btn-light">عرض الكل</a>
            </div>
            <div class="card-body">
                <?php if (empty($recent_tasks)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>لا توجد مهام</p>
                        <a href="user_tasks.php?action=add" class="btn btn-sm btn-primary">إضافة مهمة</a>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_tasks as $task): ?>
                            <div class="list-group-item border-0 px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($task['title']); ?></h6>
                                        <p class="mb-1 text-muted small"><?php echo htmlspecialchars($task['type']); ?></p>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge <?php echo $task['status'] === 'منجز' ? 'bg-success' : 'bg-warning'; ?>">
                                            <?php echo $task['status']; ?>
                                        </span>
                                        <div class="small text-muted mt-1">
                                            <?php echo date('Y-m-d', strtotime($task['date'])); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- الإنجازات الحديثة -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>
                    <i class="fas fa-trophy me-2"></i>
                    إنجازاتي الحديثة
                </span>
                <a href="user_achievements.php" class="btn btn-sm btn-light">عرض الكل</a>
            </div>
            <div class="card-body">
                <?php if (empty($recent_achievements)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-trophy fa-2x mb-2"></i>
                        <p>لا توجد إنجازات</p>
                        <a href="user_achievements.php?action=add" class="btn btn-sm btn-success">إضافة إنجاز</a>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_achievements as $achievement): ?>
                            <div class="list-group-item border-0 px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($achievement['title']); ?></h6>
                                        <p class="mb-1 text-muted small"><?php echo htmlspecialchars(substr($achievement['details'], 0, 50)) . '...'; ?></p>
                                    </div>
                                    <div class="text-end">
                                        <div class="small text-muted">
                                            <?php echo date('Y-m-d', strtotime($achievement['date'])); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- آخر تقييم -->
<?php if ($latest_evaluation): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-star me-2"></i>
                آخر تقييم (<?php echo date('Y/m', strtotime($latest_evaluation['month'])); ?>)
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">جودة العمل:</small>
                                <div class="fw-bold"><?php echo $latest_evaluation['quality_score']; ?>/5</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">الالتزام:</small>
                                <div class="fw-bold"><?php echo $latest_evaluation['punctuality_score']; ?>/5</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">التعاون:</small>
                                <div class="fw-bold"><?php echo $latest_evaluation['cooperation_score']; ?>/5</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">الإبداع:</small>
                                <div class="fw-bold"><?php echo $latest_evaluation['innovation_score']; ?>/5</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="display-6 text-primary"><?php echo number_format($latest_evaluation['total_score'], 2); ?>/20</div>
                        <?php
                        $grade_colors = [
                            'ممتاز' => 'success',
                            'جيد جداً' => 'primary',
                            'جيد' => 'info',
                            'مقبول' => 'warning',
                            'ضعيف' => 'danger'
                        ];
                        ?>
                        <span class="badge bg-<?php echo $grade_colors[$latest_evaluation['grade']] ?? 'secondary'; ?> fs-6 px-3 py-2">
                            <?php echo $latest_evaluation['grade']; ?>
                        </span>
                        <div class="mt-2">
                            <small class="text-muted">المقيم: <?php echo htmlspecialchars($latest_evaluation['evaluated_by_name']); ?></small>
                        </div>
                        <div class="mt-2">
                            <a href="user_evaluation.php" class="btn btn-sm btn-outline-primary">
                                عرض التفاصيل
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
