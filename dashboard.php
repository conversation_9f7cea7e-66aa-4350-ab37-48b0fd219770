<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkLogin();

$user = getCurrentUser();
$conn = getDBConnection();

// إحصائيات اليوم
$today = date('Y-m-d');

// إحصائيات المستخدم الحالي أو جميع المستخدمين للمدير
if ($user['role'] === 'admin') {
    // عدد المهام اليوم
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE date = ?");
    $stmt->execute([$today]);
    $tasks_today = $stmt->fetch()['count'];

    // عدد المهام المكتملة اليوم
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE date = ? AND status = 'منجز'");
    $stmt->execute([$today]);
    $completed_tasks = $stmt->fetch()['count'];

    // عدد الإنجازات اليوم
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM achievements WHERE date = ?");
    $stmt->execute([$today]);
    $achievements_today = $stmt->fetch()['count'];

    // المهام الحديثة (آخر 7 أيام)
    $week_ago = date('Y-m-d', strtotime('-7 days'));
    $stmt = $conn->prepare("
        SELECT t.*, u.full_name
        FROM tasks t
        JOIN users u ON t.user_id = u.id
        WHERE t.date >= ?
        ORDER BY t.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$week_ago]);
    $recent_tasks = $stmt->fetchAll();

    // الإنجازات الحديثة (آخر 7 أيام)
    $stmt = $conn->prepare("
        SELECT a.*, u.full_name
        FROM achievements a
        JOIN users u ON a.user_id = u.id
        WHERE a.date >= ?
        ORDER BY a.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$week_ago]);
    $recent_achievements = $stmt->fetchAll();
} else {
    // عدد المهام اليوم
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE date = ? AND user_id = ?");
    $stmt->execute([$today, $user['id']]);
    $tasks_today = $stmt->fetch()['count'];

    // عدد المهام المكتملة اليوم
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE date = ? AND status = 'منجز' AND user_id = ?");
    $stmt->execute([$today, $user['id']]);
    $completed_tasks = $stmt->fetch()['count'];

    // عدد الإنجازات اليوم
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM achievements WHERE date = ? AND user_id = ?");
    $stmt->execute([$today, $user['id']]);
    $achievements_today = $stmt->fetch()['count'];

    // المهام الحديثة (آخر 7 أيام)
    $week_ago = date('Y-m-d', strtotime('-7 days'));
    $stmt = $conn->prepare("
        SELECT t.*, u.full_name
        FROM tasks t
        JOIN users u ON t.user_id = u.id
        WHERE t.date >= ? AND t.user_id = ?
        ORDER BY t.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$week_ago, $user['id']]);
    $recent_tasks = $stmt->fetchAll();

    // الإنجازات الحديثة (آخر 7 أيام)
    $stmt = $conn->prepare("
        SELECT a.*, u.full_name
        FROM achievements a
        JOIN users u ON a.user_id = u.id
        WHERE a.date >= ? AND a.user_id = ?
        ORDER BY a.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$week_ago, $user['id']]);
    $recent_achievements = $stmt->fetchAll();
}

$page_title = 'لوحة التحكم';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-tachometer-alt me-2"></i>
                لوحة التحكم
            </h1>
            <div class="text-muted">
                <i class="fas fa-calendar-day me-1"></i>
                <?php echo date('Y-m-d'); ?>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number"><?php echo $tasks_today; ?></div>
                    <div>مهام اليوم</div>
                </div>
                <div>
                    <i class="fas fa-tasks fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number"><?php echo $completed_tasks; ?></div>
                    <div>مهام مكتملة</div>
                </div>
                <div>
                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stats-number"><?php echo $achievements_today; ?></div>
                    <div>إنجازات اليوم</div>
                </div>
                <div>
                    <i class="fas fa-trophy fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الأعمال السريعة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-bolt me-2"></i>
                الأعمال السريعة
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="tasks.php?action=add" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مهمة جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="achievements.php?action=add" class="btn btn-success w-100">
                            <i class="fas fa-trophy me-2"></i>
                            إضافة إنجاز جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="tasks.php" class="btn btn-info w-100">
                            <i class="fas fa-list me-2"></i>
                            عرض جميع المهام
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="achievements.php" class="btn btn-warning w-100">
                            <i class="fas fa-star me-2"></i>
                            عرض الإنجازات
                        </a>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <a href="reports.php" class="btn btn-secondary w-100">
                            <i class="fas fa-chart-bar me-2"></i>
                            التقارير الشهرية
                        </a>
                    </div>
                    <div class="col-md-6 mb-2">
                        <a href="reports.php?type=daily&date=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-calendar-day me-2"></i>
                            تقرير اليوم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- المهام الحديثة -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>
                    <i class="fas fa-tasks me-2"></i>
                    المهام الحديثة
                </span>
                <a href="tasks.php" class="btn btn-sm btn-light">عرض الكل</a>
            </div>
            <div class="card-body">
                <?php if (empty($recent_tasks)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p>لا توجد مهام حديثة</p>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_tasks as $task): ?>
                            <div class="list-group-item border-0 px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($task['title']); ?></h6>
                                        <p class="mb-1 text-muted small"><?php echo htmlspecialchars($task['type']); ?></p>
                                        <?php if ($user['role'] === 'admin'): ?>
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i>
                                                <?php echo htmlspecialchars($task['full_name']); ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge <?php echo $task['status'] === 'منجز' ? 'bg-success' : 'bg-warning'; ?>">
                                            <?php echo $task['status']; ?>
                                        </span>
                                        <div class="small text-muted mt-1">
                                            <?php echo date('Y-m-d', strtotime($task['date'])); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- الإنجازات الحديثة -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>
                    <i class="fas fa-trophy me-2"></i>
                    الإنجازات الحديثة
                </span>
                <a href="achievements.php" class="btn btn-sm btn-light">عرض الكل</a>
            </div>
            <div class="card-body">
                <?php if (empty($recent_achievements)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-trophy fa-2x mb-2"></i>
                        <p>لا توجد إنجازات حديثة</p>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_achievements as $achievement): ?>
                            <div class="list-group-item border-0 px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?php echo htmlspecialchars($achievement['title']); ?></h6>
                                        <p class="mb-1 text-muted small"><?php echo htmlspecialchars(substr($achievement['details'], 0, 50)) . '...'; ?></p>
                                        <?php if ($user['role'] === 'admin'): ?>
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i>
                                                <?php echo htmlspecialchars($achievement['full_name']); ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-end">
                                        <div class="small text-muted">
                                            <?php echo date('Y-m-d', strtotime($achievement['date'])); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
