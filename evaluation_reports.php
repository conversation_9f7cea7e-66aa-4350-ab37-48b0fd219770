<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkAdmin(); // فقط المدير يمكنه الوصول

$user = getCurrentUser();
$conn = getDBConnection();

$report_type = $_GET['type'] ?? 'summary';
$year = $_GET['year'] ?? date('Y');
$user_filter = $_GET['user_id'] ?? '';

// جلب التقييمات مع الإحصائيات
$where_conditions = ["YEAR(e.month) = ?"];
$params = [$year];

if (!empty($user_filter)) {
    $where_conditions[] = "e.user_id = ?";
    $params[] = $user_filter;
}

$where_clause = implode(' AND ', $where_conditions);

try {
    $stmt = $conn->prepare("
        SELECT e.*, u.full_name, u.username,
               MONTH(e.month) as eval_month,
               CASE 
                   WHEN e.total_score >= 18 THEN 'ممتاز'
                   WHEN e.total_score >= 15 THEN 'جيد جداً'
                   WHEN e.total_score >= 12 THEN 'جيد'
                   WHEN e.total_score >= 10 THEN 'مقبول'
                   ELSE 'ضعيف'
               END as calculated_grade
        FROM evaluations e
        JOIN users u ON e.user_id = u.id
        WHERE $where_clause
        ORDER BY e.month DESC, u.full_name
    ");
    $stmt->execute($params);
    $evaluations = $stmt->fetchAll();
} catch (Exception $e) {
    if (strpos($e->getMessage(), "doesn't exist") !== false) {
        $_SESSION['error'] = 'يجب تحديث جدول التقييمات أولاً لعرض التقارير المتقدمة.';
        echo "<div class='alert alert-danger text-center'>";
        echo "<h4>⚠️ جدول التقييمات يحتاج تحديث</h4>";
        echo "<p>يجب تحديث جدول التقييمات لإضافة الحقول الجديدة</p>";
        echo "<a href='update_evaluations_table.php' class='btn btn-primary'>تحديث جدول التقييمات</a>";
        echo "</div>";
        include 'includes/footer.php';
        exit();
    } else {
        throw $e;
    }
}

// حساب الإحصائيات العامة
$stats = [];
if (!empty($evaluations)) {
    $total_evaluations = count($evaluations);
    $avg_total_score = array_sum(array_column($evaluations, 'total_score')) / $total_evaluations;
    $avg_attendance = array_sum(array_column($evaluations, 'attendance_rate')) / $total_evaluations;
    $avg_task_completion = array_sum(array_column($evaluations, 'task_completion_rate')) / $total_evaluations;
    
    $grade_distribution = array_count_values(array_column($evaluations, 'grade'));
    
    $stats = [
        'total_evaluations' => $total_evaluations,
        'avg_total_score' => round($avg_total_score, 2),
        'avg_attendance' => round($avg_attendance, 1),
        'avg_task_completion' => round($avg_task_completion, 1),
        'grade_distribution' => $grade_distribution
    ];
}

// جلب المنتسبين للفلتر
$stmt = $conn->prepare("SELECT id, full_name FROM users WHERE role = 'user' ORDER BY full_name");
$stmt->execute();
$all_users = $stmt->fetchAll();

$page_title = 'التقارير المتقدمة للتقييمات';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-chart-line me-2"></i>
                التقارير المتقدمة للتقييمات
            </h1>
            <div class="btn-group">
                <a href="evaluations.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للتقييمات
                </a>
                <button class="btn btn-success" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>
                    طباعة التقرير
                </button>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر التقرير -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-filter me-2"></i>
                فلاتر التقرير
            </div>
            <div class="card-body">
                <form method="GET" action="">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="type" class="form-label">نوع التقرير</label>
                            <select class="form-select" id="type" name="type">
                                <option value="summary" <?php echo $report_type === 'summary' ? 'selected' : ''; ?>>تقرير شامل</option>
                                <option value="performance" <?php echo $report_type === 'performance' ? 'selected' : ''; ?>>تقرير الأداء</option>
                                <option value="attendance" <?php echo $report_type === 'attendance' ? 'selected' : ''; ?>>تقرير الحضور</option>
                                <option value="comparison" <?php echo $report_type === 'comparison' ? 'selected' : ''; ?>>مقارنة المنتسبين</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="year" class="form-label">السنة</label>
                            <select class="form-select" id="year" name="year">
                                <?php for ($y = date('Y') - 3; $y <= date('Y') + 1; $y++): ?>
                                    <option value="<?php echo $y; ?>" <?php echo $year == $y ? 'selected' : ''; ?>>
                                        <?php echo $y; ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="user_id" class="form-label">المنتسب</label>
                            <select class="form-select" id="user_id" name="user_id">
                                <option value="">جميع المنتسبين</option>
                                <?php foreach ($all_users as $u): ?>
                                    <option value="<?php echo $u['id']; ?>" <?php echo $user_filter == $u['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($u['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-3 mb-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-2"></i>
                                عرض التقرير
                            </button>
                            <a href="evaluation_reports.php" class="btn btn-secondary">
                                <i class="fas fa-refresh me-2"></i>
                                إعادة تعيين
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- الإحصائيات العامة -->
<?php if (!empty($stats)): ?>
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-star fa-2x mb-2"></i>
                <h3><?php echo $stats['total_evaluations']; ?></h3>
                <p class="mb-0">إجمالي التقييمات</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x mb-2"></i>
                <h3><?php echo $stats['avg_total_score']; ?>/20</h3>
                <p class="mb-0">متوسط الدرجات</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-calendar-check fa-2x mb-2"></i>
                <h3><?php echo $stats['avg_attendance']; ?>%</h3>
                <p class="mb-0">متوسط الحضور</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-tasks fa-2x mb-2"></i>
                <h3><?php echo $stats['avg_task_completion']; ?>%</h3>
                <p class="mb-0">متوسط إنجاز المهام</p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- توزيع التقديرات -->
<?php if (!empty($stats['grade_distribution'])): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie me-2"></i>
                توزيع التقديرات
            </div>
            <div class="card-body">
                <div class="row">
                    <?php
                    $grade_colors = [
                        'ممتاز' => 'success',
                        'جيد جداً' => 'primary',
                        'جيد' => 'info',
                        'مقبول' => 'warning',
                        'ضعيف' => 'danger'
                    ];
                    
                    foreach ($stats['grade_distribution'] as $grade => $count):
                        $percentage = round(($count / $stats['total_evaluations']) * 100, 1);
                    ?>
                        <div class="col-md-2">
                            <div class="card bg-<?php echo $grade_colors[$grade] ?? 'secondary'; ?> text-white">
                                <div class="card-body text-center">
                                    <h4><?php echo $count; ?></h4>
                                    <p class="mb-0"><?php echo $grade; ?></p>
                                    <small><?php echo $percentage; ?>%</small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- جدول التقييمات التفصيلي -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-table me-2"></i>
                التقييمات التفصيلية - <?php echo $year; ?>
            </div>
            <div class="card-body">
                <?php if (empty($evaluations)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد تقييمات</h5>
                        <p class="text-muted">لا توجد تقييمات مطابقة للفلاتر المحددة</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>المنتسب</th>
                                    <th>الشهر</th>
                                    <th>المجموع</th>
                                    <th>التقدير</th>
                                    <th>نسبة الحضور</th>
                                    <th>نسبة إنجاز المهام</th>
                                    <th>المهام المنجزة</th>
                                    <th>الإنجازات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($evaluations as $eval): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($eval['full_name']); ?></strong></td>
                                        <td><?php echo date('Y/m', strtotime($eval['month'])); ?></td>
                                        <td>
                                            <span class="badge bg-primary fs-6">
                                                <?php echo number_format($eval['total_score'], 2); ?>/20
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $grade_colors = [
                                                'ممتاز' => 'success',
                                                'جيد جداً' => 'primary',
                                                'جيد' => 'info',
                                                'مقبول' => 'warning',
                                                'ضعيف' => 'danger'
                                            ];
                                            ?>
                                            <span class="badge bg-<?php echo $grade_colors[$eval['grade']] ?? 'secondary'; ?>">
                                                <?php echo $eval['grade']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-info" style="width: <?php echo $eval['attendance_rate'] ?? 0; ?>%">
                                                    <?php echo round($eval['attendance_rate'] ?? 0, 1); ?>%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-success" style="width: <?php echo $eval['task_completion_rate'] ?? 0; ?>%">
                                                    <?php echo round($eval['task_completion_rate'] ?? 0, 1); ?>%
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-info"><?php echo $eval['tasks_completed']; ?></span></td>
                                        <td><span class="badge bg-success"><?php echo $eval['achievements_count']; ?></span></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="evaluations.php?action=view&id=<?php echo $eval['id']; ?>" 
                                                   class="btn btn-primary" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="print_evaluations.php?user_id=<?php echo $eval['user_id']; ?>&year=<?php echo $year; ?>&print=1" 
                                                   class="btn btn-success" target="_blank" title="طباعة التقرير الشامل">
                                                    <i class="fas fa-print"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
