<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkAdmin(); // فقط المدير يمكنه الطباعة

$user = getCurrentUser();
$conn = getDBConnection();

$user_id = $_GET['user_id'] ?? null;
$year = $_GET['year'] ?? date('Y');
$print_mode = isset($_GET['print']);

if (!$user_id) {
    $_SESSION['error'] = 'يجب تحديد المنتسب';
    header('Location: evaluations.php');
    exit();
}

// جلب بيانات المنتسب
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ? AND role = 'user'");
$stmt->execute([$user_id]);
$selected_user = $stmt->fetch();

if (!$selected_user) {
    $_SESSION['error'] = 'المنتسب غير موجود';
    header('Location: evaluations.php');
    exit();
}

// جلب تقييمات المنتسب للسنة
$stmt = $conn->prepare("
    SELECT e.*, admin.full_name as evaluated_by_name
    FROM evaluations e 
    JOIN users admin ON e.evaluated_by = admin.id
    WHERE e.user_id = ? AND YEAR(e.month) = ?
    ORDER BY e.month DESC
");
$stmt->execute([$user_id, $year]);
$evaluations = $stmt->fetchAll();

// جلب إحصائيات الحضور للسنة
$stmt = $conn->prepare("
    SELECT 
        COUNT(*) as total_days,
        SUM(CASE WHEN status = 'حاضر' THEN 1 ELSE 0 END) as present_days,
        SUM(CASE WHEN status = 'غائب' THEN 1 ELSE 0 END) as absent_days,
        SUM(CASE WHEN status = 'إجازة' THEN 1 ELSE 0 END) as leave_days,
        SUM(CASE WHEN status = 'إيفاد' THEN 1 ELSE 0 END) as mission_days,
        SUM(CASE WHEN status = 'مرض' THEN 1 ELSE 0 END) as sick_days
    FROM attendance 
    WHERE user_id = ? AND YEAR(date) = ?
");
$stmt->execute([$user_id, $year]);
$attendance_stats = $stmt->fetch();

// جلب تفاصيل الإجازات المستخدمة
$stmt = $conn->prepare("
    SELECT lr.*, lt.name as leave_type_name
    FROM leave_requests lr
    JOIN leave_types lt ON lr.leave_type_id = lt.id
    WHERE lr.user_id = ? AND lr.status = 'موافق' AND YEAR(lr.start_date) = ?
    ORDER BY lr.start_date DESC
");
$stmt->execute([$user_id, $year]);
$used_leaves = $stmt->fetchAll();

// جلب رصيد الإجازات
$stmt = $conn->prepare("
    SELECT alb.*, lt.name as leave_type_name
    FROM annual_leave_balance alb
    JOIN leave_types lt ON alb.leave_type_id = lt.id
    WHERE alb.user_id = ? AND alb.year = ?
    ORDER BY lt.name
");
$stmt->execute([$user_id, $year]);
$leave_balances = $stmt->fetchAll();

// حساب المتوسطات
$avg_scores = [];
if (!empty($evaluations)) {
    $total_quality = array_sum(array_column($evaluations, 'quality_score'));
    $total_punctuality = array_sum(array_column($evaluations, 'punctuality_score'));
    $total_cooperation = array_sum(array_column($evaluations, 'cooperation_score'));
    $total_innovation = array_sum(array_column($evaluations, 'innovation_score'));
    $total_overall = array_sum(array_column($evaluations, 'total_score'));
    $count = count($evaluations);
    
    $avg_scores = [
        'quality' => round($total_quality / $count, 2),
        'punctuality' => round($total_punctuality / $count, 2),
        'cooperation' => round($total_cooperation / $count, 2),
        'innovation' => round($total_innovation / $count, 2),
        'overall' => round($total_overall / $count, 2)
    ];
}

// حساب نسبة الحضور
$attendance_rate = 0;
if ($attendance_stats['total_days'] > 0) {
    $attendance_rate = round(($attendance_stats['present_days'] / $attendance_stats['total_days']) * 100, 1);
}

$page_title = 'تقرير شامل - ' . $selected_user['full_name'];

if (!$print_mode) {
    include 'includes/header.php';
}
?>

<?php if ($print_mode): ?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقرير الشامل - <?php echo htmlspecialchars($selected_user['full_name']); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }
        
        .print-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 0 30px rgba(0,0,0,0.2);
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #667eea;
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        
        .header h2 {
            color: #666;
            margin: 10px 0;
            font-size: 1.5rem;
        }
        
        .user-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 5px solid #667eea;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .section {
            margin: 40px 0;
            page-break-inside: avoid;
        }
        
        .section-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: bold;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }
        
        .table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
            text-align: center;
        }
        
        .table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .grade-excellent { background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; }
        .grade-very-good { background: #007bff; color: white; padding: 5px 10px; border-radius: 15px; }
        .grade-good { background: #17a2b8; color: white; padding: 5px 10px; border-radius: 15px; }
        .grade-acceptable { background: #ffc107; color: black; padding: 5px 10px; border-radius: 15px; }
        .grade-weak { background: #dc3545; color: white; padding: 5px 10px; border-radius: 15px; }
        
        .signature-section {
            margin-top: 50px;
            padding-top: 30px;
            border-top: 2px solid #667eea;
            text-align: center;
            page-break-inside: avoid;
        }
        
        .signature-line {
            border-bottom: 2px solid #333;
            width: 200px;
            margin: 20px auto;
            padding-bottom: 5px;
        }
        
        .chart-container {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            height: 25px;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            transition: width 0.3s ease;
        }
        
        @media print {
            body { margin: 0; background: white !important; }
            .print-container { box-shadow: none; }
            .section { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="header">
            <h1>شعبة السيطرة والإنترنت</h1>
            <h2>التقرير الشامل للمنتسب</h2>
        </div>
        
        <div class="user-info">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <strong>اسم المنتسب:</strong> <?php echo htmlspecialchars($selected_user['full_name']); ?><br>
                    <strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($selected_user['username']); ?><br>
                    <strong>السنة:</strong> <?php echo $year; ?>
                </div>
                <div>
                    <strong>عدد التقييمات:</strong> <?php echo count($evaluations); ?> تقييم<br>
                    <strong>نسبة الحضور:</strong> <?php echo $attendance_rate; ?>%<br>
                    <strong>تاريخ التقرير:</strong> <?php echo date('Y/m/d H:i'); ?>
                </div>
            </div>
        </div>

        <!-- إحصائيات الحضور -->
        <div class="section">
            <div class="section-title">📊 إحصائيات الحضور والغياب</div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $attendance_stats['total_days'] ?? 0; ?></div>
                    <div>إجمالي الأيام</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $attendance_stats['present_days'] ?? 0; ?></div>
                    <div>أيام الحضور</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $attendance_stats['absent_days'] ?? 0; ?></div>
                    <div>أيام الغياب</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $attendance_rate; ?>%</div>
                    <div>نسبة الحضور</div>
                </div>
            </div>
            
            <div class="chart-container">
                <h4>توزيع أيام العمل:</h4>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                    <div>
                        <strong>أيام الإجازات:</strong> <?php echo $attendance_stats['leave_days'] ?? 0; ?> يوم
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: <?php echo $attendance_stats['total_days'] > 0 ? (($attendance_stats['leave_days'] ?? 0) / $attendance_stats['total_days']) * 100 : 0; ?>%">
                                <?php echo $attendance_stats['leave_days'] ?? 0; ?>
                            </div>
                        </div>
                    </div>
                    <div>
                        <strong>أيام الإيفاد:</strong> <?php echo $attendance_stats['mission_days'] ?? 0; ?> يوم
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: <?php echo $attendance_stats['total_days'] > 0 ? (($attendance_stats['mission_days'] ?? 0) / $attendance_stats['total_days']) * 100 : 0; ?>%">
                                <?php echo $attendance_stats['mission_days'] ?? 0; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- رصيد الإجازات -->
        <div class="section">
            <div class="section-title">💰 رصيد الإجازات السنوي</div>
            
            <?php if (!empty($leave_balances)): ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>نوع الإجازة</th>
                            <th>الأيام المخصصة</th>
                            <th>الأيام المستخدمة</th>
                            <th>الأيام المتبقية</th>
                            <th>نسبة الاستخدام</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($leave_balances as $balance): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($balance['leave_type_name']); ?></strong></td>
                                <td><?php echo $balance['total_days']; ?></td>
                                <td><?php echo $balance['used_days']; ?></td>
                                <td><?php echo $balance['remaining_days']; ?></td>
                                <td>
                                    <?php 
                                    $usage_percent = $balance['total_days'] > 0 ? round(($balance['used_days'] / $balance['total_days']) * 100, 1) : 0;
                                    echo $usage_percent . '%';
                                    ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p style="text-align: center; color: #666;">لا توجد بيانات رصيد إجازات</p>
            <?php endif; ?>
        </div>

        <!-- الإجازات المستخدمة -->
        <div class="section">
            <div class="section-title">📅 تفاصيل الإجازات المستخدمة</div>
            
            <?php if (!empty($used_leaves)): ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>نوع الإجازة</th>
                            <th>من</th>
                            <th>إلى</th>
                            <th>عدد الأيام</th>
                            <th>السبب</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($used_leaves as $leave): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($leave['leave_type_name']); ?></td>
                                <td><?php echo date('Y/m/d', strtotime($leave['start_date'])); ?></td>
                                <td><?php echo date('Y/m/d', strtotime($leave['end_date'])); ?></td>
                                <td><?php echo $leave['days_count']; ?></td>
                                <td><?php echo htmlspecialchars(substr($leave['reason'], 0, 50)) . (strlen($leave['reason']) > 50 ? '...' : ''); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p style="text-align: center; color: #666;">لم يتم استخدام أي إجازات هذا العام</p>
            <?php endif; ?>
        </div>

        <!-- التقييمات الشهرية -->
        <div class="section">
            <div class="section-title">⭐ التقييمات الشهرية</div>
            
            <?php if (!empty($evaluations)): ?>
                <table class="table">
                    <thead>
                        <tr>
                            <th>الشهر</th>
                            <th>جودة العمل</th>
                            <th>الالتزام</th>
                            <th>التعاون</th>
                            <th>الإبداع</th>
                            <th>المجموع</th>
                            <th>التقدير</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($evaluations as $eval): ?>
                            <tr>
                                <td><?php echo date('Y/m', strtotime($eval['month'])); ?></td>
                                <td><?php echo $eval['quality_score']; ?>/5</td>
                                <td><?php echo $eval['punctuality_score']; ?>/5</td>
                                <td><?php echo $eval['cooperation_score']; ?>/5</td>
                                <td><?php echo $eval['innovation_score']; ?>/5</td>
                                <td><?php echo number_format($eval['total_score'], 2); ?>/20</td>
                                <td>
                                    <?php
                                    $grade_classes = [
                                        'ممتاز' => 'grade-excellent',
                                        'جيد جداً' => 'grade-very-good',
                                        'جيد' => 'grade-good',
                                        'مقبول' => 'grade-acceptable',
                                        'ضعيف' => 'grade-weak'
                                    ];
                                    ?>
                                    <span class="<?php echo $grade_classes[$eval['grade']] ?? 'grade-acceptable'; ?>">
                                        <?php echo $eval['grade']; ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <!-- المتوسطات -->
                <?php if (!empty($avg_scores)): ?>
                <div class="chart-container">
                    <h4>متوسط الدرجات السنوي:</h4>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $avg_scores['quality']; ?></div>
                            <div>جودة العمل</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $avg_scores['punctuality']; ?></div>
                            <div>الالتزام</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $avg_scores['cooperation']; ?></div>
                            <div>التعاون</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $avg_scores['overall']; ?></div>
                            <div>المجموع الكلي</div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            <?php else: ?>
                <p style="text-align: center; color: #666;">لا توجد تقييمات لهذا العام</p>
            <?php endif; ?>
        </div>
        
        <div class="signature-section">
            <p><strong>مسؤول شعبة السيطرة والإنترنت</strong></p>
            <div class="signature-line"></div>
            <p><strong>محمد حسن محمد</strong></p>
            <p style="margin-top: 30px; color: #666;">
                تم إنشاء هذا التقرير بواسطة نظام إدارة شعبة السيطرة والإنترنت<br>
                تاريخ الطباعة: <?php echo date('Y/m/d H:i:s'); ?>
            </p>
        </div>
    </div>
    
    <script>
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>

<?php else: ?>
<!-- واجهة اختيار المنتسب والسنة للطباعة -->
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-print me-2"></i>
                طباعة التقارير الشاملة
            </h1>
            <a href="evaluations.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للتقييمات
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-user-check me-2"></i>
                اختيار المنتسب والسنة
            </div>
            <div class="card-body">
                <form method="GET" action="">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="user_id" class="form-label">المنتسب *</label>
                            <select class="form-select" id="user_id" name="user_id" required>
                                <option value="">اختر المنتسب</option>
                                <?php
                                $stmt = $conn->prepare("SELECT id, full_name FROM users WHERE role = 'user' ORDER BY full_name");
                                $stmt->execute();
                                $all_users = $stmt->fetchAll();
                                
                                foreach ($all_users as $u):
                                ?>
                                    <option value="<?php echo $u['id']; ?>" <?php echo $user_id == $u['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($u['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="year" class="form-label">السنة *</label>
                            <select class="form-select" id="year" name="year" required>
                                <?php for ($y = date('Y') - 3; $y <= date('Y') + 1; $y++): ?>
                                    <option value="<?php echo $y; ?>" <?php echo $year == $y ? 'selected' : ''; ?>>
                                        <?php echo $y; ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-eye me-2"></i>
                            معاينة التقرير
                        </button>
                        <?php if ($user_id): ?>
                            <a href="print_evaluations.php?user_id=<?php echo $user_id; ?>&year=<?php echo $year; ?>&print=1" 
                               class="btn btn-success" target="_blank">
                                <i class="fas fa-print me-2"></i>
                                طباعة التقرير
                            </a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php if ($user_id && $selected_user): ?>
<!-- معاينة التقرير -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>
                    <i class="fas fa-file-alt me-2"></i>
                    معاينة التقرير - <?php echo htmlspecialchars($selected_user['full_name']); ?> (<?php echo $year; ?>)
                </span>
                <a href="print_evaluations.php?user_id=<?php echo $user_id; ?>&year=<?php echo $year; ?>&print=1" 
                   class="btn btn-success" target="_blank">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </a>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4><?php echo count($evaluations); ?></h4>
                                <p class="mb-0">تقييمات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4><?php echo $attendance_rate; ?>%</h4>
                                <p class="mb-0">نسبة الحضور</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h4><?php echo count($used_leaves); ?></h4>
                                <p class="mb-0">إجازات مستخدمة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h4><?php echo !empty($avg_scores) ? $avg_scores['overall'] : 'N/A'; ?></h4>
                                <p class="mb-0">متوسط التقييم</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>محتويات التقرير:</strong>
                    إحصائيات الحضور والغياب، رصيد الإجازات السنوي، تفاصيل الإجازات المستخدمة، التقييمات الشهرية مع المتوسطات
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php endif; ?>

<?php if (!$print_mode) include 'includes/footer.php'; ?>
