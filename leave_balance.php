<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkLogin();

$user = getCurrentUser();
$conn = getDBConnection();
$year_filter = $_GET['year'] ?? date('Y');
$user_filter = $_GET['user_id'] ?? ($user['role'] === 'admin' ? '' : $user['id']);

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST' && $user['role'] === 'admin') {
    $post_action = $_POST['action'] ?? '';
    
    if ($post_action === 'update_balance') {
        $balance_id = $_POST['balance_id'] ?? '';
        $total_days = intval($_POST['total_days'] ?? 0);
        
        if (!empty($balance_id) && $total_days >= 0) {
            try {
                // الحصول على الأيام المستخدمة
                $stmt = $conn->prepare("SELECT used_days FROM annual_leave_balance WHERE id = ?");
                $stmt->execute([$balance_id]);
                $current_balance = $stmt->fetch();
                
                if ($current_balance) {
                    $remaining_days = $total_days - $current_balance['used_days'];
                    
                    $stmt = $conn->prepare("UPDATE annual_leave_balance SET total_days = ?, remaining_days = ? WHERE id = ?");
                    $stmt->execute([$total_days, $remaining_days, $balance_id]);
                    $_SESSION['success'] = 'تم تحديث رصيد الإجازات بنجاح';
                }
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء تحديث الرصيد';
            }
        }
        header('Location: leave_balance.php?year=' . $year_filter . '&user_id=' . $user_filter);
        exit();
    }
    
    elseif ($post_action === 'initialize_balances') {
        $year = intval($_POST['year'] ?? date('Y'));
        
        try {
            // جلب جميع المنتسبين
            $stmt = $conn->prepare("SELECT id FROM users WHERE role = 'user'");
            $stmt->execute();
            $users = $stmt->fetchAll();
            
            // جلب أنواع الإجازات
            $stmt = $conn->prepare("SELECT id, max_days_per_year FROM leave_types");
            $stmt->execute();
            $leave_types = $stmt->fetchAll();
            
            $initialized_count = 0;
            
            foreach ($users as $u) {
                foreach ($leave_types as $type) {
                    // التحقق من عدم وجود رصيد للسنة
                    $stmt = $conn->prepare("SELECT id FROM annual_leave_balance WHERE user_id = ? AND year = ? AND leave_type_id = ?");
                    $stmt->execute([$u['id'], $year, $type['id']]);
                    
                    if (!$stmt->fetch()) {
                        $stmt = $conn->prepare("INSERT INTO annual_leave_balance (user_id, year, leave_type_id, total_days, used_days, remaining_days) VALUES (?, ?, ?, ?, 0, ?)");
                        $stmt->execute([$u['id'], $year, $type['id'], $type['max_days_per_year'], $type['max_days_per_year']]);
                        $initialized_count++;
                    }
                }
            }
            
            $_SESSION['success'] = "تم تهيئة $initialized_count رصيد إجازة للسنة $year";
        } catch (Exception $e) {
            $_SESSION['error'] = 'حدث خطأ أثناء تهيئة الأرصدة';
        }
        
        header('Location: leave_balance.php?year=' . $year);
        exit();
    }
}

// التحقق من وجود جداول رصيد الإجازات
try {
    $stmt = $conn->prepare("SELECT 1 FROM annual_leave_balance LIMIT 1");
    $stmt->execute();
} catch (PDOException $e) {
    if (strpos($e->getMessage(), "doesn't exist") !== false) {
        $_SESSION['error'] = 'جداول رصيد الإجازات غير موجودة. يرجى تحديث قاعدة البيانات أولاً.';
        echo "<div class='alert alert-danger text-center'>";
        echo "<h4>⚠️ جداول رصيد الإجازات غير موجودة</h4>";
        echo "<p>يجب تحديث قاعدة البيانات أولاً لإضافة جداول الحضور والإجازات</p>";
        echo "<a href='update_attendance_db.php' class='btn btn-primary'>تحديث قاعدة البيانات</a>";
        echo "</div>";
        include 'includes/footer.php';
        exit();
    } else {
        throw $e;
    }
}

// جلب بيانات رصيد الإجازات
$where_conditions = ["alb.year = ?"];
$params = [$year_filter];

if (!empty($user_filter)) {
    $where_conditions[] = "alb.user_id = ?";
    $params[] = $user_filter;
}

$where_clause = implode(' AND ', $where_conditions);

$stmt = $conn->prepare("
    SELECT alb.*, u.full_name, lt.name as leave_type_name, lt.is_unlimited,
           lt.is_deductible, lt.color_code, lt.icon, lt.priority_order
    FROM annual_leave_balance alb
    JOIN users u ON alb.user_id = u.id
    JOIN leave_types lt ON alb.leave_type_id = lt.id
    WHERE $where_clause
    ORDER BY u.full_name, lt.priority_order, lt.name
");
$stmt->execute($params);
$balances = $stmt->fetchAll();

// جلب المنتسبين للفلتر
$stmt = $conn->prepare("SELECT id, full_name FROM users WHERE role = 'user' ORDER BY full_name");
$stmt->execute();
$all_users = $stmt->fetchAll();

// حساب الإحصائيات
$stats = [];
if (!empty($balances)) {
    $total_allocated = array_sum(array_column($balances, 'total_days'));
    $total_used = array_sum(array_column($balances, 'used_days'));
    $total_remaining = array_sum(array_column($balances, 'remaining_days'));
    
    $stats = [
        'total_allocated' => $total_allocated,
        'total_used' => $total_used,
        'total_remaining' => $total_remaining,
        'usage_percentage' => $total_allocated > 0 ? round(($total_used / $total_allocated) * 100, 1) : 0
    ];
}

$page_title = 'رصيد الإجازات';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-chart-pie me-2"></i>
                رصيد الإجازات
            </h1>
            <div class="btn-group">
                <?php if ($user['role'] === 'admin'): ?>
                    <button class="btn btn-success" onclick="showInitializeModal()">
                        <i class="fas fa-plus me-2"></i>
                        تهيئة أرصدة السنة
                    </button>
                    <a href="attendance.php" class="btn btn-info">
                        <i class="fas fa-calendar-check me-2"></i>
                        إدارة الحضور
                    </a>
                <?php endif; ?>
                <a href="leave_requests.php" class="btn btn-primary">
                    <i class="fas fa-file-alt me-2"></i>
                    طلبات الإجازات
                </a>
                <a href="print_leave_details.php" class="btn btn-info">
                    <i class="fas fa-print me-2"></i>
                    طباعة تفاصيل الإجازات
                </a>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-filter me-2"></i>
                فلاتر البحث
            </div>
            <div class="card-body">
                <form method="GET" action="">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="year" class="form-label">السنة</label>
                            <select class="form-select" id="year" name="year">
                                <?php for ($y = date('Y') - 2; $y <= date('Y') + 1; $y++): ?>
                                    <option value="<?php echo $y; ?>" <?php echo $year_filter == $y ? 'selected' : ''; ?>>
                                        <?php echo $y; ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        
                        <?php if ($user['role'] === 'admin'): ?>
                        <div class="col-md-4 mb-3">
                            <label for="user_id" class="form-label">المنتسب</label>
                            <select class="form-select" id="user_id" name="user_id">
                                <option value="">جميع المنتسبين</option>
                                <?php foreach ($all_users as $u): ?>
                                    <option value="<?php echo $u['id']; ?>" <?php echo $user_filter == $u['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($u['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <?php endif; ?>
                        
                        <div class="col-md-4 mb-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                            <a href="leave_balance.php" class="btn btn-secondary">
                                <i class="fas fa-refresh me-2"></i>
                                إعادة تعيين
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- الإحصائيات -->
<?php if (!empty($stats)): ?>
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-calendar fa-2x mb-2"></i>
                <h3><?php echo $stats['total_allocated']; ?></h3>
                <p class="mb-0">إجمالي الأيام المخصصة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-calendar-minus fa-2x mb-2"></i>
                <h3><?php echo $stats['total_used']; ?></h3>
                <p class="mb-0">الأيام المستخدمة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-calendar-plus fa-2x mb-2"></i>
                <h3><?php echo $stats['total_remaining']; ?></h3>
                <p class="mb-0">الأيام المتبقية</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-percentage fa-2x mb-2"></i>
                <h3><?php echo $stats['usage_percentage']; ?>%</h3>
                <p class="mb-0">نسبة الاستخدام</p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- جدول رصيد الإجازات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>
                    <i class="fas fa-table me-2"></i>
                    رصيد الإجازات لسنة <?php echo $year_filter; ?>
                </span>
                <button class="btn btn-sm btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-1"></i>
                    طباعة
                </button>
            </div>
            <div class="card-body">
                <?php if (empty($balances)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد أرصدة إجازات</h5>
                        <p class="text-muted">لم يتم تهيئة أرصدة الإجازات لهذه السنة</p>
                        <?php if ($user['role'] === 'admin'): ?>
                            <button class="btn btn-success" onclick="showInitializeModal()">
                                <i class="fas fa-plus me-2"></i>
                                تهيئة أرصدة السنة
                            </button>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <?php if ($user['role'] === 'admin' && empty($user_filter)): ?>
                                        <th>المنتسب</th>
                                    <?php endif; ?>
                                    <th>نوع الإجازة</th>
                                    <th>الأيام المخصصة</th>
                                    <th>الأيام المستخدمة</th>
                                    <th>الأيام المتبقية</th>
                                    <th>نسبة الاستخدام</th>
                                    <?php if ($user['role'] === 'admin'): ?>
                                        <th>الإجراءات</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($balances as $balance): ?>
                                    <tr>
                                        <?php if ($user['role'] === 'admin' && empty($user_filter)): ?>
                                            <td><strong><?php echo htmlspecialchars($balance['full_name']); ?></strong></td>
                                        <?php endif; ?>
                                        <td>
                                            <span class="badge" style="background-color: <?php echo $balance['color_code']; ?>">
                                                <i class="<?php echo $balance['icon']; ?> me-1"></i>
                                                <?php echo htmlspecialchars($balance['leave_type_name']); ?>
                                            </span>
                                            <?php if (!$balance['is_deductible']): ?>
                                                <br><small class="text-muted">لا يستقطع من الرصيد</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($balance['is_unlimited']): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-infinity me-1"></i>
                                                    غير محدود
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-primary"><?php echo $balance['total_days']; ?> يوم</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($balance['is_unlimited']): ?>
                                                <span class="badge bg-secondary">-</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning"><?php echo $balance['used_days']; ?> يوم</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($balance['is_unlimited']): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-infinity me-1"></i>
                                                    غير محدود
                                                </span>
                                            <?php else: ?>
                                                <span class="badge <?php echo $balance['remaining_days'] > 0 ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo $balance['remaining_days']; ?> يوم
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($balance['is_unlimited']): ?>
                                                <div class="text-center">
                                                    <i class="fas fa-infinity text-success"></i>
                                                </div>
                                            <?php else: ?>
                                                <?php
                                                $usage_percent = $balance['total_days'] > 0 ? round(($balance['used_days'] / $balance['total_days']) * 100, 1) : 0;
                                                $progress_color = $usage_percent <= 50 ? 'success' : ($usage_percent <= 80 ? 'warning' : 'danger');
                                                ?>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-<?php echo $progress_color; ?>"
                                                         style="width: <?php echo $usage_percent; ?>%">
                                                        <?php echo $usage_percent; ?>%
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <?php if ($user['role'] === 'admin'): ?>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-primary"
                                                            onclick="showUpdateModal(<?php echo $balance['id']; ?>, <?php echo $balance['total_days']; ?>, '<?php echo htmlspecialchars($balance['full_name']); ?>', '<?php echo htmlspecialchars($balance['leave_type_name']); ?>')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <a href="print_leave_details.php?user_id=<?php echo $balance['user_id']; ?>&year=<?php echo $year_filter; ?>&print=1"
                                                       class="btn btn-success" target="_blank" title="طباعة تفاصيل الإجازات">
                                                        <i class="fas fa-print"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        <?php endif; ?>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تهيئة الأرصدة -->
<div class="modal fade" id="initializeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تهيئة أرصدة الإجازات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="">
                <div class="modal-body">
                    <input type="hidden" name="action" value="initialize_balances">
                    
                    <div class="mb-3">
                        <label for="init_year" class="form-label">السنة</label>
                        <select class="form-select" id="init_year" name="year" required>
                            <?php for ($y = date('Y') - 1; $y <= date('Y') + 2; $y++): ?>
                                <option value="<?php echo $y; ?>" <?php echo $y == date('Y') ? 'selected' : ''; ?>>
                                    <?php echo $y; ?>
                                </option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم إنشاء أرصدة إجازات لجميع المنتسبين حسب الأنواع المحددة في النظام.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>
                        تهيئة الأرصدة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تحديث الرصيد -->
<div class="modal fade" id="updateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث رصيد الإجازة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_balance">
                    <input type="hidden" name="balance_id" id="update_balance_id">
                    
                    <div class="mb-3">
                        <label class="form-label">المنتسب:</label>
                        <p class="fw-bold" id="update_user_name"></p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">نوع الإجازة:</label>
                        <p class="fw-bold" id="update_leave_type"></p>
                    </div>
                    
                    <div class="mb-3">
                        <label for="total_days" class="form-label">إجمالي الأيام المخصصة</label>
                        <input type="number" class="form-control" id="total_days" name="total_days" min="0" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showInitializeModal() {
    const modal = new bootstrap.Modal(document.getElementById('initializeModal'));
    modal.show();
}

function showUpdateModal(balanceId, totalDays, userName, leaveType) {
    document.getElementById('update_balance_id').value = balanceId;
    document.getElementById('total_days').value = totalDays;
    document.getElementById('update_user_name').textContent = userName;
    document.getElementById('update_leave_type').textContent = leaveType;
    
    const modal = new bootstrap.Modal(document.getElementById('updateModal'));
    modal.show();
}
</script>

<?php include 'includes/footer.php'; ?>
