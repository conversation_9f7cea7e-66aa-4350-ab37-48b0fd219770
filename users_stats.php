<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkAdmin(); // فقط المدير يمكنه الوصول

$user = getCurrentUser();
$conn = getDBConnection();

// إحصائيات عامة
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM users");
$stmt->execute();
$total_users = $stmt->fetch()['total'];

$stmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
$stmt->execute();
$admin_count = $stmt->fetch()['count'];

$stmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'user'");
$stmt->execute();
$user_count = $stmt->fetch()['count'];

// إحصائيات المهام والإنجازات لكل منتسب
$stmt = $conn->prepare("
    SELECT 
        u.id,
        u.full_name,
        u.username,
        u.created_at,
        COUNT(DISTINCT t.id) as total_tasks,
        COUNT(DISTINCT CASE WHEN t.status = 'منجز' THEN t.id END) as completed_tasks,
        COUNT(DISTINCT a.id) as total_achievements,
        COUNT(DISTINCT e.id) as evaluations_count,
        AVG(e.total_score) as avg_score
    FROM users u
    LEFT JOIN tasks t ON u.id = t.user_id
    LEFT JOIN achievements a ON u.id = a.user_id
    LEFT JOIN evaluations e ON u.id = e.user_id
    WHERE u.role = 'user'
    GROUP BY u.id, u.full_name, u.username, u.created_at
    ORDER BY total_tasks DESC, completed_tasks DESC
");
$stmt->execute();
$users_stats = $stmt->fetchAll();

// إحصائيات شهرية
$current_month = date('Y-m');
$stmt = $conn->prepare("
    SELECT 
        u.full_name,
        COUNT(DISTINCT t.id) as monthly_tasks,
        COUNT(DISTINCT CASE WHEN t.status = 'منجز' THEN t.id END) as monthly_completed,
        COUNT(DISTINCT a.id) as monthly_achievements
    FROM users u
    LEFT JOIN tasks t ON u.id = t.user_id AND DATE_FORMAT(t.date, '%Y-%m') = ?
    LEFT JOIN achievements a ON u.id = a.user_id AND DATE_FORMAT(a.date, '%Y-%m') = ?
    WHERE u.role = 'user'
    GROUP BY u.id, u.full_name
    HAVING monthly_tasks > 0 OR monthly_achievements > 0
    ORDER BY monthly_completed DESC, monthly_achievements DESC
");
$stmt->execute([$current_month, $current_month]);
$monthly_stats = $stmt->fetchAll();

$page_title = 'إحصائيات المنتسبين';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-chart-pie me-2"></i>
                إحصائيات المنتسبين
            </h1>
            <div class="btn-group">
                <a href="users_management.php" class="btn btn-secondary">
                    <i class="fas fa-users me-2"></i>
                    إدارة المنتسبين
                </a>
                <button class="btn btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات عامة -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h3><?php echo $total_users; ?></h3>
                <p class="mb-0">إجمالي المستخدمين</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <i class="fas fa-user-shield fa-2x mb-2"></i>
                <h3><?php echo $admin_count; ?></h3>
                <p class="mb-0">المديرين</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-user fa-2x mb-2"></i>
                <h3><?php echo $user_count; ?></h3>
                <p class="mb-0">المنتسبين</p>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات الأداء الشامل -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar me-2"></i>
                إحصائيات الأداء الشامل
            </div>
            <div class="card-body">
                <?php if (empty($users_stats)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد بيانات أداء</h5>
                        <p class="text-muted">لم يتم تسجيل أي مهام أو إنجازات بعد</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>المنتسب</th>
                                    <th>إجمالي المهام</th>
                                    <th>المهام المكتملة</th>
                                    <th>معدل الإنجاز</th>
                                    <th>الإنجازات</th>
                                    <th>التقييمات</th>
                                    <th>متوسط الدرجات</th>
                                    <th>تاريخ الانضمام</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users_stats as $stat): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($stat['full_name']); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($stat['username']); ?></small>
                                        </td>
                                        <td><span class="badge bg-info"><?php echo $stat['total_tasks']; ?></span></td>
                                        <td><span class="badge bg-success"><?php echo $stat['completed_tasks']; ?></span></td>
                                        <td>
                                            <?php 
                                            $completion_rate = $stat['total_tasks'] > 0 ? ($stat['completed_tasks'] / $stat['total_tasks']) * 100 : 0;
                                            $rate_class = $completion_rate >= 80 ? 'success' : ($completion_rate >= 60 ? 'warning' : 'danger');
                                            ?>
                                            <span class="badge bg-<?php echo $rate_class; ?>">
                                                <?php echo number_format($completion_rate, 1); ?>%
                                            </span>
                                        </td>
                                        <td><span class="badge bg-warning"><?php echo $stat['total_achievements']; ?></span></td>
                                        <td><span class="badge bg-primary"><?php echo $stat['evaluations_count']; ?></span></td>
                                        <td>
                                            <?php if ($stat['avg_score']): ?>
                                                <strong><?php echo number_format($stat['avg_score'], 2); ?>/20</strong>
                                            <?php else: ?>
                                                <span class="text-muted">لا يوجد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('Y/m/d', strtotime($stat['created_at'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات الشهر الحالي -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-calendar-month me-2"></i>
                إحصائيات الشهر الحالي (<?php echo date('Y/m'); ?>)
            </div>
            <div class="card-body">
                <?php if (empty($monthly_stats)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد أنشطة هذا الشهر</h5>
                        <p class="text-muted">لم يتم تسجيل أي مهام أو إنجازات في الشهر الحالي</p>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($monthly_stats as $stat): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card border-left-primary">
                                    <div class="card-body">
                                        <h6 class="card-title text-primary">
                                            <?php echo htmlspecialchars($stat['full_name']); ?>
                                        </h6>
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <div class="h5 text-info"><?php echo $stat['monthly_tasks']; ?></div>
                                                <small class="text-muted">مهام</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="h5 text-success"><?php echo $stat['monthly_completed']; ?></div>
                                                <small class="text-muted">مكتملة</small>
                                            </div>
                                            <div class="col-4">
                                                <div class="h5 text-warning"><?php echo $stat['monthly_achievements']; ?></div>
                                                <small class="text-muted">إنجازات</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 4px solid #007bff !important;
}

@media print {
    .btn-group, .no-print {
        display: none !important;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
