<?php
// ملف تحديث قاعدة البيانات لإضافة جدول التقييمات
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تحديث قاعدة البيانات</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }";
echo ".container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }";
echo ".success { color: green; } .error { color: red; } .info { color: blue; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔄 تحديث قاعدة البيانات</h1>";

try {
    $conn = getDBConnection();
    echo "<p class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // التحقق من وجود جدول التقييمات
    $stmt = $conn->prepare("SHOW TABLES LIKE 'evaluations'");
    $stmt->execute();
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "<p class='info'>ℹ️ جدول التقييمات موجود بالفعل</p>";
    } else {
        echo "<p class='info'>📝 إنشاء جدول التقييمات...</p>";
        
        // إنشاء جدول التقييمات
        $create_table_sql = "
        CREATE TABLE evaluations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            month DATE NOT NULL,
            tasks_completed INT DEFAULT 0,
            achievements_count INT DEFAULT 0,
            quality_score DECIMAL(3,2) DEFAULT 0.00,
            punctuality_score DECIMAL(3,2) DEFAULT 0.00,
            cooperation_score DECIMAL(3,2) DEFAULT 0.00,
            innovation_score DECIMAL(3,2) DEFAULT 0.00,
            total_score DECIMAL(5,2) DEFAULT 0.00,
            grade ENUM('ممتاز', 'جيد جداً', 'جيد', 'مقبول', 'ضعيف') DEFAULT 'مقبول',
            notes TEXT,
            evaluated_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (evaluated_by) REFERENCES users(id),
            UNIQUE KEY unique_user_month (user_id, month)
        )";
        
        $conn->exec($create_table_sql);
        echo "<p class='success'>✅ تم إنشاء جدول التقييمات بنجاح</p>";
        
        // إضافة تقييم تجريبي
        echo "<p class='info'>📝 إضافة بيانات تجريبية...</p>";
        
        $insert_sample_sql = "
        INSERT INTO evaluations (user_id, month, tasks_completed, achievements_count, quality_score, punctuality_score, cooperation_score, innovation_score, total_score, grade, notes, evaluated_by) 
        VALUES (2, DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01'), 15, 8, 4.50, 4.20, 4.80, 4.00, 17.50, 'جيد جداً', 'منتسب متميز في الأداء والالتزام', 1)
        ";
        
        try {
            $conn->exec($insert_sample_sql);
            echo "<p class='success'>✅ تم إضافة البيانات التجريبية</p>";
        } catch (Exception $e) {
            echo "<p class='error'>⚠️ تحذير: لم يتم إضافة البيانات التجريبية (قد تكون موجودة بالفعل)</p>";
        }
    }
    
    // التحقق من البيانات
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM evaluations");
    $stmt->execute();
    $evaluations_count = $stmt->fetch()['count'];
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📊 إحصائيات التقييمات:</h3>";
    echo "<p>⭐ عدد التقييمات: $evaluations_count</p>";
    echo "</div>";
    
    echo "<p class='success'>🎉 تم التحديث بنجاح!</p>";
    echo "<p><a href='evaluations.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 الانتقال إلى تقييم المنتسبين</a></p>";
    echo "<p><a href='dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>🏠 العودة للوحة التحكم</a></p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في التحديث: " . $e->getMessage() . "</p>";
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>تأكد من:</h3>";
    echo "<ul>";
    echo "<li>تشغيل XAMPP (Apache + MySQL)</li>";
    echo "<li>وجود قاعدة البيانات control_internet_db</li>";
    echo "<li>وجود الجداول الأساسية (users, tasks, achievements)</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><a href='install.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 إعادة تثبيت قاعدة البيانات</a></p>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
