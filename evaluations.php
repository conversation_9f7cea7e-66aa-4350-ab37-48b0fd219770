<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkAdmin(); // فقط المدير يمكنه الوصول

$user = getCurrentUser();
$conn = getDBConnection();
$action = $_GET['action'] ?? 'list';
$evaluation_id = $_GET['id'] ?? null;
$user_id = $_GET['user_id'] ?? null;

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $post_action = $_POST['action'] ?? '';
    
    if ($post_action === 'add' || $post_action === 'edit') {
        $eval_user_id = $_POST['user_id'] ?? '';
        $month = $_POST['month'] ?? '';
        $quality_score = floatval($_POST['quality_score'] ?? 0);
        $punctuality_score = floatval($_POST['punctuality_score'] ?? 0);
        $cooperation_score = floatval($_POST['cooperation_score'] ?? 0);
        $innovation_score = floatval($_POST['innovation_score'] ?? 0);
        $notes = trim($_POST['notes'] ?? '');
        
        // حساب المجموع الكلي
        $total_score = $quality_score + $punctuality_score + $cooperation_score + $innovation_score;
        
        // تحديد التقدير
        if ($total_score >= 18) $grade = 'ممتاز';
        elseif ($total_score >= 16) $grade = 'جيد جداً';
        elseif ($total_score >= 14) $grade = 'جيد';
        elseif ($total_score >= 12) $grade = 'مقبول';
        else $grade = 'ضعيف';
        
        // حساب عدد المهام والإنجازات للشهر
        $month_start = $month . '-01';
        $month_end = date('Y-m-t', strtotime($month_start));

        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks WHERE user_id = ? AND date BETWEEN ? AND ? AND status = 'منجز'");
        $stmt->execute([$eval_user_id, $month_start, $month_end]);
        $tasks_completed = $stmt->fetch()['count'];

        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM achievements WHERE user_id = ? AND date BETWEEN ? AND ?");
        $stmt->execute([$eval_user_id, $month_start, $month_end]);
        $achievements_count = $stmt->fetch()['count'];

        // حساب إحصائيات الحضور للشهر
        $stmt = $conn->prepare("SELECT COUNT(*) as total_days FROM attendance WHERE user_id = ? AND date BETWEEN ? AND ?");
        $stmt->execute([$eval_user_id, $month_start, $month_end]);
        $total_attendance_days = $stmt->fetch()['count'];

        $stmt = $conn->prepare("SELECT COUNT(*) as present_days FROM attendance WHERE user_id = ? AND date BETWEEN ? AND ? AND status = 'حاضر'");
        $stmt->execute([$eval_user_id, $month_start, $month_end]);
        $present_days = $stmt->fetch()['count'];

        $stmt = $conn->prepare("SELECT COUNT(*) as absent_days FROM attendance WHERE user_id = ? AND date BETWEEN ? AND ? AND status = 'غائب'");
        $stmt->execute([$eval_user_id, $month_start, $month_end]);
        $absent_days = $stmt->fetch()['count'];

        // حساب نسبة الحضور (تؤثر على درجة الالتزام)
        $attendance_rate = $total_attendance_days > 0 ? ($present_days / $total_attendance_days) * 100 : 100;

        // تعديل درجة الالتزام بناءً على نسبة الحضور
        if ($attendance_rate < 80) {
            $punctuality_score = max(1, $punctuality_score - 1); // خصم درجة للحضور الضعيف
        } elseif ($attendance_rate >= 95) {
            $punctuality_score = min(5, $punctuality_score + 0.25); // إضافة ربع درجة للحضور الممتاز
        }
        
        if ($post_action === 'add') {
            try {
                $stmt = $conn->prepare("
                    INSERT INTO evaluations (user_id, month, tasks_completed, achievements_count, quality_score, punctuality_score, cooperation_score, innovation_score, total_score, grade, notes, evaluated_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([$eval_user_id, $month_start, $tasks_completed, $achievements_count, $quality_score, $punctuality_score, $cooperation_score, $innovation_score, $total_score, $grade, $notes, $user['id']]);
                $_SESSION['success'] = 'تم إضافة التقييم بنجاح';
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'unique_user_month') !== false) {
                    $_SESSION['error'] = 'يوجد تقييم لهذا المنتسب في نفس الشهر بالفعل';
                } else {
                    $_SESSION['error'] = 'حدث خطأ أثناء إضافة التقييم';
                }
            }
        } else {
            try {
                $stmt = $conn->prepare("
                    UPDATE evaluations SET tasks_completed = ?, achievements_count = ?, quality_score = ?, punctuality_score = ?, cooperation_score = ?, innovation_score = ?, total_score = ?, grade = ?, notes = ? 
                    WHERE id = ?
                ");
                $stmt->execute([$tasks_completed, $achievements_count, $quality_score, $punctuality_score, $cooperation_score, $innovation_score, $total_score, $grade, $notes, $_POST['id']]);
                $_SESSION['success'] = 'تم تحديث التقييم بنجاح';
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء تحديث التقييم';
            }
        }
        header('Location: evaluations.php');
        exit();
    }
    
    elseif ($post_action === 'delete') {
        $id = $_POST['id'] ?? '';
        if (!empty($id)) {
            try {
                $stmt = $conn->prepare("DELETE FROM evaluations WHERE id = ?");
                $stmt->execute([$id]);
                $_SESSION['success'] = 'تم حذف التقييم بنجاح';
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء حذف التقييم';
            }
        }
        header('Location: evaluations.php');
        exit();
    }
}

// جلب التقييمات مع التحقق من وجود الجدول
try {
    $stmt = $conn->prepare("
        SELECT e.*, u.full_name, admin.full_name as evaluated_by_name
        FROM evaluations e
        JOIN users u ON e.user_id = u.id
        JOIN users admin ON e.evaluated_by = admin.id
        ORDER BY e.month DESC, e.created_at DESC
    ");
    $stmt->execute();
    $evaluations = $stmt->fetchAll();
} catch (PDOException $e) {
    if (strpos($e->getMessage(), "doesn't exist") !== false) {
        // الجدول غير موجود - توجيه لصفحة التحديث
        $_SESSION['error'] = 'جدول التقييمات غير موجود. يرجى تحديث قاعدة البيانات أولاً.';
        header('Location: update_database.php');
        exit();
    } else {
        throw $e;
    }
}

// جلب قائمة المنتسبين
$stmt = $conn->prepare("SELECT id, full_name FROM users WHERE role = 'user' ORDER BY full_name");
$stmt->execute();
$users_list = $stmt->fetchAll();

// جلب تقييم للتعديل أو العرض
$edit_evaluation = null;
$view_evaluation = null;
if (($action === 'edit' || $action === 'view') && $evaluation_id) {
    $stmt = $conn->prepare("
        SELECT e.*, u.full_name, admin.full_name as evaluated_by_name
        FROM evaluations e
        JOIN users u ON e.user_id = u.id
        JOIN users admin ON e.evaluated_by = admin.id
        WHERE e.id = ?
    ");
    $stmt->execute([$evaluation_id]);
    $evaluation_data = $stmt->fetch();

    if (!$evaluation_data) {
        $_SESSION['error'] = 'لم يتم العثور على التقييم';
        header('Location: evaluations.php');
        exit();
    }

    if ($action === 'edit') {
        $edit_evaluation = $evaluation_data;
    } else {
        $view_evaluation = $evaluation_data;
    }
}

$page_title = 'تقييم المنتسبين';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-star me-2"></i>
                تقييم المنتسبين
            </h1>
            <?php if ($action !== 'add' && $action !== 'edit' && $action !== 'view'): ?>
                <a href="evaluations.php?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة تقييم جديد
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php if ($action === 'view'): ?>
<!-- عرض تفاصيل التقييم -->
<div class="row">
    <div class="col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-eye me-2"></i>
                تفاصيل تقييم <?php echo htmlspecialchars($view_evaluation['full_name']); ?>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5 class="text-primary">معلومات التقييم</h5>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>المنتسب:</strong></td>
                                <td><?php echo htmlspecialchars($view_evaluation['full_name']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>الشهر:</strong></td>
                                <td><?php echo date('Y/m', strtotime($view_evaluation['month'])); ?></td>
                            </tr>
                            <tr>
                                <td><strong>المهام المنجزة:</strong></td>
                                <td><span class="badge bg-info"><?php echo $view_evaluation['tasks_completed']; ?></span></td>
                            </tr>
                            <tr>
                                <td><strong>الإنجازات:</strong></td>
                                <td><span class="badge bg-success"><?php echo $view_evaluation['achievements_count']; ?></span></td>
                            </tr>
                            <tr>
                                <td><strong>المقيم:</strong></td>
                                <td><?php echo htmlspecialchars($view_evaluation['evaluated_by_name']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ التقييم:</strong></td>
                                <td><?php echo date('Y/m/d H:i', strtotime($view_evaluation['created_at'])); ?></td>
                            </tr>
                        </table>
                    </div>

                    <div class="col-md-6">
                        <h5 class="text-primary">النتيجة النهائية</h5>
                        <div class="text-center">
                            <div class="display-4 text-primary mb-2">
                                <?php echo number_format($view_evaluation['total_score'], 2); ?>/20
                            </div>
                            <?php
                            $grade_colors = [
                                'ممتاز' => 'success',
                                'جيد جداً' => 'primary',
                                'جيد' => 'info',
                                'مقبول' => 'warning',
                                'ضعيف' => 'danger'
                            ];
                            ?>
                            <span class="badge bg-<?php echo $grade_colors[$view_evaluation['grade']] ?? 'secondary'; ?> fs-5 px-4 py-2">
                                <?php echo $view_evaluation['grade']; ?>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="text-primary">تفاصيل الدرجات</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6>جودة العمل</h6>
                                        <div class="h4 text-primary"><?php echo $view_evaluation['quality_score']; ?>/5</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6>الالتزام والانضباط</h6>
                                        <div class="h4 text-success"><?php echo $view_evaluation['punctuality_score']; ?>/5</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6>التعاون وروح الفريق</h6>
                                        <div class="h4 text-info"><?php echo $view_evaluation['cooperation_score']; ?>/5</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6>الإبداع والمبادرة</h6>
                                        <div class="h4 text-warning"><?php echo $view_evaluation['innovation_score']; ?>/5</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (!empty($view_evaluation['notes'])): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="text-primary">ملاحظات وتوصيات</h5>
                        <div class="alert alert-info">
                            <?php echo nl2br(htmlspecialchars($view_evaluation['notes'])); ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <div class="d-flex gap-2">
                    <a href="evaluations.php?action=edit&id=<?php echo $view_evaluation['id']; ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>
                        تعديل التقييم
                    </a>
                    <a href="evaluations.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للقائمة
                    </a>
                    <button class="btn btn-info" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php elseif ($action === 'add' || $action === 'edit'): ?>
<!-- نموذج إضافة/تعديل التقييم -->
<div class="row">
    <div class="col-md-10 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?> me-2"></i>
                <?php echo $action === 'add' ? 'إضافة تقييم جديد' : 'تعديل التقييم'; ?>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="action" value="<?php echo $action; ?>">
                    <?php if ($action === 'edit'): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_evaluation['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="user_id" class="form-label">المنتسب *</label>
                            <select class="form-select" id="user_id" name="user_id" required <?php echo $action === 'edit' ? 'disabled' : ''; ?>>
                                <option value="">اختر المنتسب</option>
                                <?php foreach ($users_list as $u): ?>
                                    <option value="<?php echo $u['id']; ?>" <?php echo ($edit_evaluation['user_id'] ?? '') == $u['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($u['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <?php if ($action === 'edit'): ?>
                                <input type="hidden" name="user_id" value="<?php echo $edit_evaluation['user_id']; ?>">
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="month" class="form-label">الشهر *</label>
                            <input type="month" class="form-control" id="month" name="month" 
                                   value="<?php echo $edit_evaluation ? date('Y-m', strtotime($edit_evaluation['month'])) : date('Y-m', strtotime('-1 month')); ?>" 
                                   required <?php echo $action === 'edit' ? 'readonly' : ''; ?>>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <h5 class="text-primary">معايير التقييم (من 1 إلى 5)</h5>
                            <small class="text-muted">5 = ممتاز، 4 = جيد جداً، 3 = جيد، 2 = مقبول، 1 = ضعيف</small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="quality_score" class="form-label">جودة العمل *</label>
                            <select class="form-select" id="quality_score" name="quality_score" required>
                                <option value="">اختر التقييم</option>
                                <?php for ($i = 1; $i <= 5; $i += 0.25): ?>
                                    <option value="<?php echo $i; ?>" <?php echo ($edit_evaluation['quality_score'] ?? '') == $i ? 'selected' : ''; ?>>
                                        <?php echo $i; ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="punctuality_score" class="form-label">الالتزام والانضباط *</label>
                            <select class="form-select" id="punctuality_score" name="punctuality_score" required>
                                <option value="">اختر التقييم</option>
                                <?php for ($i = 1; $i <= 5; $i += 0.25): ?>
                                    <option value="<?php echo $i; ?>" <?php echo ($edit_evaluation['punctuality_score'] ?? '') == $i ? 'selected' : ''; ?>>
                                        <?php echo $i; ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="cooperation_score" class="form-label">التعاون وروح الفريق *</label>
                            <select class="form-select" id="cooperation_score" name="cooperation_score" required>
                                <option value="">اختر التقييم</option>
                                <?php for ($i = 1; $i <= 5; $i += 0.25): ?>
                                    <option value="<?php echo $i; ?>" <?php echo ($edit_evaluation['cooperation_score'] ?? '') == $i ? 'selected' : ''; ?>>
                                        <?php echo $i; ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="innovation_score" class="form-label">الإبداع والمبادرة *</label>
                            <select class="form-select" id="innovation_score" name="innovation_score" required>
                                <option value="">اختر التقييم</option>
                                <?php for ($i = 1; $i <= 5; $i += 0.25): ?>
                                    <option value="<?php echo $i; ?>" <?php echo ($edit_evaluation['innovation_score'] ?? '') == $i ? 'selected' : ''; ?>>
                                        <?php echo $i; ?>
                                    </option>
                                <?php endfor; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات وتوصيات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="4" 
                                  placeholder="اكتب ملاحظاتك وتوصياتك هنا..."><?php echo htmlspecialchars($edit_evaluation['notes'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            <?php echo $action === 'add' ? 'إضافة التقييم' : 'حفظ التغييرات'; ?>
                        </button>
                        <a href="evaluations.php" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php else: ?>
<!-- قائمة التقييمات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>
                    <i class="fas fa-list me-2"></i>
                    قائمة التقييمات (<?php echo count($evaluations); ?> تقييم)
                </span>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="window.print()">
                        <i class="fas fa-print me-1"></i>
                        طباعة
                    </button>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($evaluations)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-star fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد تقييمات مسجلة</h5>
                        <p class="text-muted">ابدأ بإضافة تقييم جديد للمنتسبين</p>
                        <a href="evaluations.php?action=add" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة تقييم جديد
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>المنتسب</th>
                                    <th>الشهر</th>
                                    <th>المهام المنجزة</th>
                                    <th>الإنجازات</th>
                                    <th>المجموع</th>
                                    <th>التقدير</th>
                                    <th>المقيم</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($evaluations as $evaluation): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($evaluation['full_name']); ?></strong></td>
                                        <td><?php echo date('Y/m', strtotime($evaluation['month'])); ?></td>
                                        <td><span class="badge bg-info"><?php echo $evaluation['tasks_completed']; ?></span></td>
                                        <td><span class="badge bg-success"><?php echo $evaluation['achievements_count']; ?></span></td>
                                        <td><strong><?php echo number_format($evaluation['total_score'], 2); ?>/20</strong></td>
                                        <td>
                                            <?php
                                            $grade_colors = [
                                                'ممتاز' => 'bg-success',
                                                'جيد جداً' => 'bg-primary',
                                                'جيد' => 'bg-info',
                                                'مقبول' => 'bg-warning',
                                                'ضعيف' => 'bg-danger'
                                            ];
                                            ?>
                                            <span class="badge <?php echo $grade_colors[$evaluation['grade']] ?? 'bg-secondary'; ?>">
                                                <?php echo $evaluation['grade']; ?>
                                            </span>
                                        </td>
                                        <td><small><?php echo htmlspecialchars($evaluation['evaluated_by_name']); ?></small></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="evaluations.php?action=view&id=<?php echo $evaluation['id']; ?>" class="btn btn-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="evaluations.php?action=edit&id=<?php echo $evaluation['id']; ?>" class="btn btn-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form method="POST" style="display: inline;" onsubmit="return confirmDelete('هل أنت متأكد من حذف هذا التقييم؟')">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="id" value="<?php echo $evaluation['id']; ?>">
                                                    <button type="submit" class="btn btn-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
