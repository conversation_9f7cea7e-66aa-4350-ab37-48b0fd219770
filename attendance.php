<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkAdmin(); // فقط المدير يمكنه الوصول

$user = getCurrentUser();
$conn = getDBConnection();
$action = $_GET['action'] ?? 'list';
$date_filter = $_GET['date'] ?? date('Y-m-d');
$user_filter = $_GET['user_id'] ?? '';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $post_action = $_POST['action'] ?? '';
    
    if ($post_action === 'mark_attendance') {
        $attendance_data = $_POST['attendance'] ?? [];
        $date = $_POST['date'] ?? date('Y-m-d');
        
        foreach ($attendance_data as $user_id => $data) {
            $status = $data['status'] ?? 'حاضر';
            $check_in = !empty($data['check_in']) ? $data['check_in'] : null;
            $check_out = !empty($data['check_out']) ? $data['check_out'] : null;
            $notes = trim($data['notes'] ?? '');
            
            try {
                // التحقق من وجود سجل للتاريخ
                $stmt = $conn->prepare("SELECT id FROM attendance WHERE user_id = ? AND date = ?");
                $stmt->execute([$user_id, $date]);
                $existing = $stmt->fetch();
                
                if ($existing) {
                    // تحديث السجل الموجود
                    $stmt = $conn->prepare("UPDATE attendance SET status = ?, check_in_time = ?, check_out_time = ?, notes = ?, created_by = ? WHERE user_id = ? AND date = ?");
                    $stmt->execute([$status, $check_in, $check_out, $notes, $user['id'], $user_id, $date]);
                } else {
                    // إنشاء سجل جديد
                    $stmt = $conn->prepare("INSERT INTO attendance (user_id, date, status, check_in_time, check_out_time, notes, created_by) VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$user_id, $date, $status, $check_in, $check_out, $notes, $user['id']]);
                }
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء حفظ بيانات الحضور';
                break;
            }
        }
        
        if (!isset($_SESSION['error'])) {
            $_SESSION['success'] = 'تم حفظ بيانات الحضور بنجاح';
        }
        
        header('Location: attendance.php?date=' . $date);
        exit();
    }
}

// جلب المنتسبين
$users_query = "SELECT id, full_name FROM users WHERE role = 'user' ORDER BY full_name";
if (!empty($user_filter)) {
    $users_query = "SELECT id, full_name FROM users WHERE role = 'user' AND id = ? ORDER BY full_name";
}

$stmt = $conn->prepare($users_query);
if (!empty($user_filter)) {
    $stmt->execute([$user_filter]);
} else {
    $stmt->execute();
}
$users = $stmt->fetchAll();

// جلب بيانات الحضور للتاريخ المحدد
$attendance_data = [];
if (!empty($users)) {
    $user_ids = array_column($users, 'id');
    $placeholders = str_repeat('?,', count($user_ids) - 1) . '?';
    
    $stmt = $conn->prepare("SELECT * FROM attendance WHERE user_id IN ($placeholders) AND date = ?");
    $stmt->execute(array_merge($user_ids, [$date_filter]));
    $attendance_records = $stmt->fetchAll();
    
    foreach ($attendance_records as $record) {
        $attendance_data[$record['user_id']] = $record;
    }
}

// جلب جميع المنتسبين للفلتر
$stmt = $conn->prepare("SELECT id, full_name FROM users WHERE role = 'user' ORDER BY full_name");
$stmt->execute();
$all_users = $stmt->fetchAll();

$page_title = 'إدارة الحضور والغياب';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-calendar-check me-2"></i>
                إدارة الحضور والغياب
            </h1>
            <div class="btn-group">
                <a href="leave_requests.php" class="btn btn-info">
                    <i class="fas fa-file-alt me-2"></i>
                    طلبات الإجازات
                </a>
                <a href="leave_balance.php" class="btn btn-warning">
                    <i class="fas fa-chart-pie me-2"></i>
                    رصيد الإجازات
                </a>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-filter me-2"></i>
                فلاتر البحث
            </div>
            <div class="card-body">
                <form method="GET" action="">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="date" class="form-label">التاريخ</label>
                            <input type="date" class="form-control" id="date" name="date" value="<?php echo $date_filter; ?>">
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="user_id" class="form-label">المنتسب</label>
                            <select class="form-select" id="user_id" name="user_id">
                                <option value="">جميع المنتسبين</option>
                                <?php foreach ($all_users as $u): ?>
                                    <option value="<?php echo $u['id']; ?>" <?php echo $user_filter == $u['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($u['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-4 mb-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                            <a href="attendance.php" class="btn btn-secondary">
                                <i class="fas fa-refresh me-2"></i>
                                إعادة تعيين
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول الحضور -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>
                    <i class="fas fa-users me-2"></i>
                    الحضور ليوم <?php echo date('Y/m/d', strtotime($date_filter)); ?>
                </span>
                <button class="btn btn-sm btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-1"></i>
                    طباعة
                </button>
            </div>
            <div class="card-body">
                <?php if (empty($users)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد منتسبين</h5>
                        <p class="text-muted">لا يوجد منتسبين مطابقين للفلتر المحدد</p>
                    </div>
                <?php else: ?>
                    <form method="POST" action="">
                        <input type="hidden" name="action" value="mark_attendance">
                        <input type="hidden" name="date" value="<?php echo $date_filter; ?>">
                        
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>المنتسب</th>
                                        <th>الحالة</th>
                                        <th>وقت الدخول</th>
                                        <th>وقت الخروج</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $u): ?>
                                        <?php $attendance = $attendance_data[$u['id']] ?? null; ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($u['full_name']); ?></strong>
                                            </td>
                                            <td>
                                                <select class="form-select form-select-sm" name="attendance[<?php echo $u['id']; ?>][status]" onchange="toggleTimeFields(this, <?php echo $u['id']; ?>)">
                                                    <option value="حاضر" <?php echo ($attendance['status'] ?? 'حاضر') === 'حاضر' ? 'selected' : ''; ?>>حاضر</option>
                                                    <option value="غائب" <?php echo ($attendance['status'] ?? '') === 'غائب' ? 'selected' : ''; ?>>غائب</option>
                                                    <option value="إجازة" <?php echo ($attendance['status'] ?? '') === 'إجازة' ? 'selected' : ''; ?>>إجازة</option>
                                                    <option value="إيفاد" <?php echo ($attendance['status'] ?? '') === 'إيفاد' ? 'selected' : ''; ?>>إيفاد</option>
                                                    <option value="مرض" <?php echo ($attendance['status'] ?? '') === 'مرض' ? 'selected' : ''; ?>>مرض</option>
                                                </select>
                                            </td>
                                            <td>
                                                <input type="time" class="form-control form-control-sm time-field-<?php echo $u['id']; ?>" 
                                                       name="attendance[<?php echo $u['id']; ?>][check_in]" 
                                                       value="<?php echo $attendance['check_in_time'] ?? ''; ?>"
                                                       <?php echo ($attendance['status'] ?? 'حاضر') !== 'حاضر' ? 'disabled' : ''; ?>>
                                            </td>
                                            <td>
                                                <input type="time" class="form-control form-control-sm time-field-<?php echo $u['id']; ?>" 
                                                       name="attendance[<?php echo $u['id']; ?>][check_out]" 
                                                       value="<?php echo $attendance['check_out_time'] ?? ''; ?>"
                                                       <?php echo ($attendance['status'] ?? 'حاضر') !== 'حاضر' ? 'disabled' : ''; ?>>
                                            </td>
                                            <td>
                                                <input type="text" class="form-control form-control-sm" 
                                                       name="attendance[<?php echo $u['id']; ?>][notes]" 
                                                       value="<?php echo htmlspecialchars($attendance['notes'] ?? ''); ?>"
                                                       placeholder="ملاحظات...">
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <button type="button" class="btn btn-success btn-sm" onclick="markAllPresent()">
                                    <i class="fas fa-check-circle me-1"></i>
                                    تحديد الكل حاضر
                                </button>
                                <button type="button" class="btn btn-secondary btn-sm" onclick="setDefaultTimes()">
                                    <i class="fas fa-clock me-1"></i>
                                    أوقات افتراضية
                                </button>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ بيانات الحضور
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
function toggleTimeFields(selectElement, userId) {
    const timeFields = document.querySelectorAll('.time-field-' + userId);
    const isPresent = selectElement.value === 'حاضر';
    
    timeFields.forEach(field => {
        field.disabled = !isPresent;
        if (!isPresent) {
            field.value = '';
        }
    });
}

function markAllPresent() {
    const statusSelects = document.querySelectorAll('select[name*="[status]"]');
    statusSelects.forEach(select => {
        select.value = 'حاضر';
        const userId = select.name.match(/\[(\d+)\]/)[1];
        toggleTimeFields(select, userId);
    });
}

function setDefaultTimes() {
    const checkInFields = document.querySelectorAll('input[name*="[check_in]"]');
    const checkOutFields = document.querySelectorAll('input[name*="[check_out]"]');
    
    checkInFields.forEach(field => {
        if (!field.disabled && !field.value) {
            field.value = '08:00';
        }
    });
    
    checkOutFields.forEach(field => {
        if (!field.disabled && !field.value) {
            field.value = '16:00';
        }
    });
}

// تطبيق حالة الحقول عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const statusSelects = document.querySelectorAll('select[name*="[status]"]');
    statusSelects.forEach(select => {
        const userId = select.name.match(/\[(\d+)\]/)[1];
        toggleTimeFields(select, userId);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
