<?php
// ملف التثبيت التلقائي
echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تثبيت نظام شعبة السيطرة والإنترنت</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }";
echo ".container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }";
echo ".success { color: green; } .error { color: red; } .info { color: blue; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🚀 تثبيت نظام شعبة السيطرة والإنترنت</h1>";

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $conn = new PDO(
        "mysql:host=localhost;charset=utf8mb4",
        "root",
        "",
        array(
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        )
    );
    
    echo "<p class='success'>✅ تم الاتصال بـ MySQL بنجاح</p>";
    
    // إنشاء قاعدة البيانات
    $conn->exec("CREATE DATABASE IF NOT EXISTS control_internet_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p class='success'>✅ تم إنشاء قاعدة البيانات control_internet_db</p>";
    
    // الاتصال بقاعدة البيانات الجديدة
    $conn->exec("USE control_internet_db");
    
    // قراءة وتنفيذ ملف database.sql
    $sql_content = file_get_contents('database.sql');
    
    if ($sql_content === false) {
        throw new Exception("لا يمكن قراءة ملف database.sql");
    }
    
    // تقسيم الاستعلامات
    $queries = explode(';', $sql_content);
    
    foreach ($queries as $query) {
        $query = trim($query);
        if (!empty($query) && !preg_match('/^(CREATE DATABASE|USE)/i', $query)) {
            try {
                $conn->exec($query);
            } catch (PDOException $e) {
                // تجاهل أخطاء الجداول الموجودة بالفعل
                if (strpos($e->getMessage(), 'already exists') === false) {
                    echo "<p class='error'>تحذير: " . $e->getMessage() . "</p>";
                }
            }
        }
    }
    
    echo "<p class='success'>✅ تم إنشاء جميع الجداول بنجاح</p>";

    // التأكد من وجود جدول التقييمات
    echo "<p class='info'>🔍 التحقق من جدول التقييمات...</p>";
    try {
        $conn->exec("SELECT 1 FROM evaluations LIMIT 1");
        echo "<p class='success'>✅ جدول التقييمات موجود</p>";
    } catch (PDOException $e) {
        echo "<p class='info'>📝 إنشاء جدول التقييمات...</p>";
        $evaluations_sql = "
        CREATE TABLE evaluations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            month DATE NOT NULL,
            tasks_completed INT DEFAULT 0,
            achievements_count INT DEFAULT 0,
            quality_score DECIMAL(3,2) DEFAULT 0.00,
            punctuality_score DECIMAL(3,2) DEFAULT 0.00,
            cooperation_score DECIMAL(3,2) DEFAULT 0.00,
            innovation_score DECIMAL(3,2) DEFAULT 0.00,
            total_score DECIMAL(5,2) DEFAULT 0.00,
            grade ENUM('ممتاز', 'جيد جداً', 'جيد', 'مقبول', 'ضعيف') DEFAULT 'مقبول',
            notes TEXT,
            evaluated_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (evaluated_by) REFERENCES users(id),
            UNIQUE KEY unique_user_month (user_id, month)
        )";
        $conn->exec($evaluations_sql);
        echo "<p class='success'>✅ تم إنشاء جدول التقييمات</p>";

        // إضافة تقييم تجريبي
        try {
            $sample_evaluation = "INSERT INTO evaluations (user_id, month, tasks_completed, achievements_count, quality_score, punctuality_score, cooperation_score, innovation_score, total_score, grade, notes, evaluated_by) VALUES (2, DATE_FORMAT(CURDATE() - INTERVAL 1 MONTH, '%Y-%m-01'), 15, 8, 4.50, 4.20, 4.80, 4.00, 17.50, 'جيد جداً', 'منتسب متميز في الأداء والالتزام', 1)";
            $conn->exec($sample_evaluation);
            echo "<p class='success'>✅ تم إضافة تقييم تجريبي</p>";
        } catch (Exception $e) {
            echo "<p class='info'>ℹ️ تم تخطي إضافة التقييم التجريبي</p>";
        }
    }

    // التحقق من البيانات
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $users_count = $stmt->fetch()['count'];
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks");
    $stmt->execute();
    $tasks_count = $stmt->fetch()['count'];
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM achievements");
    $stmt->execute();
    $achievements_count = $stmt->fetch()['count'];

    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM evaluations");
    $stmt->execute();
    $evaluations_count = $stmt->fetch()['count'];

    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📊 إحصائيات قاعدة البيانات:</h3>";
    echo "<p>👥 المستخدمين: $users_count</p>";
    echo "<p>📋 المهام: $tasks_count</p>";
    echo "<p>🏆 الإنجازات: $achievements_count</p>";
    echo "<p>⭐ التقييمات: $evaluations_count</p>";
    echo "</div>";
    
    echo "<div style='background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🔑 الحسابات الافتراضية:</h3>";
    echo "<p><strong>المدير:</strong> admin / password</p>";
    echo "<p><strong>منتسب:</strong> user1 / password</p>";
    echo "</div>";
    
    echo "<p class='success'>🎉 تم التثبيت بنجاح!</p>";
    echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 بدء استخدام النظام</a></p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في التثبيت: " . $e->getMessage() . "</p>";
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>تأكد من:</h3>";
    echo "<ul>";
    echo "<li>تشغيل XAMPP (Apache + MySQL)</li>";
    echo "<li>وجود ملف database.sql في نفس المجلد</li>";
    echo "<li>صلاحيات الكتابة في قاعدة البيانات</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
