<?php
// ملف التثبيت التلقائي
echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تثبيت نظام شعبة السيطرة والإنترنت</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }";
echo ".container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }";
echo ".success { color: green; } .error { color: red; } .info { color: blue; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🚀 تثبيت نظام شعبة السيطرة والإنترنت</h1>";

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $conn = new PDO(
        "mysql:host=localhost;charset=utf8mb4",
        "root",
        "",
        array(
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        )
    );
    
    echo "<p class='success'>✅ تم الاتصال بـ MySQL بنجاح</p>";
    
    // إنشاء قاعدة البيانات
    $conn->exec("CREATE DATABASE IF NOT EXISTS control_internet_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p class='success'>✅ تم إنشاء قاعدة البيانات control_internet_db</p>";
    
    // الاتصال بقاعدة البيانات الجديدة
    $conn->exec("USE control_internet_db");
    
    // قراءة وتنفيذ ملف database.sql
    $sql_content = file_get_contents('database.sql');
    
    if ($sql_content === false) {
        throw new Exception("لا يمكن قراءة ملف database.sql");
    }
    
    // تقسيم الاستعلامات
    $queries = explode(';', $sql_content);
    
    foreach ($queries as $query) {
        $query = trim($query);
        if (!empty($query) && !preg_match('/^(CREATE DATABASE|USE)/i', $query)) {
            try {
                $conn->exec($query);
            } catch (PDOException $e) {
                // تجاهل أخطاء الجداول الموجودة بالفعل
                if (strpos($e->getMessage(), 'already exists') === false) {
                    echo "<p class='error'>تحذير: " . $e->getMessage() . "</p>";
                }
            }
        }
    }
    
    echo "<p class='success'>✅ تم إنشاء جميع الجداول بنجاح</p>";
    
    // التحقق من البيانات
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $users_count = $stmt->fetch()['count'];
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tasks");
    $stmt->execute();
    $tasks_count = $stmt->fetch()['count'];
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM achievements");
    $stmt->execute();
    $achievements_count = $stmt->fetch()['count'];

    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM evaluations");
    $stmt->execute();
    $evaluations_count = $stmt->fetch()['count'];

    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📊 إحصائيات قاعدة البيانات:</h3>";
    echo "<p>👥 المستخدمين: $users_count</p>";
    echo "<p>📋 المهام: $tasks_count</p>";
    echo "<p>🏆 الإنجازات: $achievements_count</p>";
    echo "<p>⭐ التقييمات: $evaluations_count</p>";
    echo "</div>";
    
    echo "<div style='background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🔑 الحسابات الافتراضية:</h3>";
    echo "<p><strong>المدير:</strong> admin / password</p>";
    echo "<p><strong>منتسب:</strong> user1 / password</p>";
    echo "</div>";
    
    echo "<p class='success'>🎉 تم التثبيت بنجاح!</p>";
    echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 بدء استخدام النظام</a></p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في التثبيت: " . $e->getMessage() . "</p>";
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>تأكد من:</h3>";
    echo "<ul>";
    echo "<li>تشغيل XAMPP (Apache + MySQL)</li>";
    echo "<li>وجود ملف database.sql في نفس المجلد</li>";
    echo "<li>صلاحيات الكتابة في قاعدة البيانات</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
