<?php
// ملف تحديث قاعدة البيانات لإضافة جداول الحضور والإجازات
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تحديث قاعدة البيانات - الحضور والإجازات</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }";
echo ".container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }";
echo ".success { color: green; } .error { color: red; } .info { color: blue; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔄 تحديث قاعدة البيانات - نظام الحضور والإجازات</h1>";

try {
    $conn = getDBConnection();
    echo "<p class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // قراءة وتنفيذ ملف attendance_tables.sql
    $sql_content = file_get_contents('attendance_tables.sql');
    
    if ($sql_content === false) {
        throw new Exception("لا يمكن قراءة ملف attendance_tables.sql");
    }
    
    echo "<p class='info'>📝 تنفيذ أوامر إنشاء الجداول...</p>";
    
    // تقسيم الاستعلامات
    $queries = explode(';', $sql_content);
    $executed_count = 0;
    
    foreach ($queries as $query) {
        $query = trim($query);
        if (!empty($query) && !preg_match('/^(--|#)/', $query)) {
            try {
                $conn->exec($query);
                $executed_count++;
            } catch (PDOException $e) {
                // تجاهل أخطاء الجداول الموجودة بالفعل
                if (strpos($e->getMessage(), 'already exists') === false && 
                    strpos($e->getMessage(), 'Duplicate entry') === false) {
                    echo "<p class='error'>تحذير: " . $e->getMessage() . "</p>";
                }
            }
        }
    }
    
    echo "<p class='success'>✅ تم تنفيذ $executed_count استعلام بنجاح</p>";
    
    // التحقق من الجداول الجديدة
    $tables_to_check = ['attendance', 'leave_types', 'leave_requests', 'annual_leave_balance'];
    
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM $table");
            $stmt->execute();
            $count = $stmt->fetch()['count'];
            echo "<p class='success'>✅ جدول $table: موجود ($count سجل)</p>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ جدول $table: غير موجود أو به مشكلة</p>";
        }
    }
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📊 الجداول الجديدة:</h3>";
    echo "<ul>";
    echo "<li><strong>attendance:</strong> جدول الحضور والغياب اليومي</li>";
    echo "<li><strong>leave_types:</strong> أنواع الإجازات (سنوية، مرضية، طارئة، إيفاد، إلخ)</li>";
    echo "<li><strong>leave_requests:</strong> طلبات الإجازات من المنتسبين</li>";
    echo "<li><strong>annual_leave_balance:</strong> رصيد الإجازات السنوي لكل منتسب</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎯 المميزات الجديدة:</h3>";
    echo "<ul>";
    echo "<li>تسجيل الحضور والغياب اليومي</li>";
    echo "<li>إدارة طلبات الإجازات</li>";
    echo "<li>تتبع رصيد الإجازات السنوي</li>";
    echo "<li>ربط الحضور بتقييم المنتسبين</li>";
    echo "<li>أنواع متعددة من الإجازات</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p class='success'>🎉 تم التحديث بنجاح!</p>";
    echo "<p><a href='attendance.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📅 إدارة الحضور</a></p>";
    echo "<p><a href='leave_requests.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>📝 طلبات الإجازات</a></p>";
    echo "<p><a href='leave_balance.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>📊 رصيد الإجازات</a></p>";
    echo "<p><a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>🏠 العودة للوحة التحكم</a></p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في التحديث: " . $e->getMessage() . "</p>";
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>تأكد من:</h3>";
    echo "<ul>";
    echo "<li>تشغيل XAMPP (Apache + MySQL)</li>";
    echo "<li>وجود قاعدة البيانات control_internet_db</li>";
    echo "<li>وجود ملف attendance_tables.sql في نفس المجلد</li>";
    echo "<li>صلاحيات الكتابة في قاعدة البيانات</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><a href='install.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 إعادة تثبيت قاعدة البيانات</a></p>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
