<?php
// ملف تحديث قاعدة البيانات لإضافة جداول الحضور والإجازات
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تحديث قاعدة البيانات - الحضور والإجازات</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }";
echo ".container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }";
echo ".success { color: green; } .error { color: red; } .info { color: blue; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔄 تحديث قاعدة البيانات - نظام الحضور والإجازات</h1>";

try {
    $conn = getDBConnection();
    echo "<p class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    echo "<p class='info'>📝 إنشاء جداول الحضور والإجازات...</p>";

    // إنشاء الجداول مباشرة
    $tables_sql = [
        // جدول الحضور والغياب
        "CREATE TABLE IF NOT EXISTS attendance (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            date DATE NOT NULL,
            status ENUM('حاضر', 'غائب', 'إجازة', 'إيفاد', 'مرض') NOT NULL DEFAULT 'حاضر',
            check_in_time TIME NULL,
            check_out_time TIME NULL,
            notes TEXT,
            created_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (created_by) REFERENCES users(id),
            UNIQUE KEY unique_user_date (user_id, date)
        )",

        // جدول أنواع الإجازات
        "CREATE TABLE IF NOT EXISTS leave_types (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            max_days_per_year INT DEFAULT 30,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",

        // جدول طلبات الإجازات
        "CREATE TABLE IF NOT EXISTS leave_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            leave_type_id INT NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            days_count INT NOT NULL,
            reason TEXT,
            status ENUM('معلق', 'موافق', 'مرفوض') DEFAULT 'معلق',
            approved_by INT NULL,
            approved_at TIMESTAMP NULL,
            rejection_reason TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (leave_type_id) REFERENCES leave_types(id),
            FOREIGN KEY (approved_by) REFERENCES users(id)
        )",

        // جدول رصيد الإجازات السنوي
        "CREATE TABLE IF NOT EXISTS annual_leave_balance (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            year YEAR NOT NULL,
            leave_type_id INT NOT NULL,
            total_days INT DEFAULT 30,
            used_days INT DEFAULT 0,
            remaining_days INT DEFAULT 30,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (leave_type_id) REFERENCES leave_types(id),
            UNIQUE KEY unique_user_year_type (user_id, year, leave_type_id)
        )"
    ];

    $executed_count = 0;
    foreach ($tables_sql as $sql) {
        try {
            $conn->exec($sql);
            $executed_count++;
            echo "<p class='success'>✅ تم إنشاء جدول بنجاح</p>";
        } catch (PDOException $e) {
            echo "<p class='error'>❌ خطأ في إنشاء جدول: " . $e->getMessage() . "</p>";
        }
    }

    // إدراج أنواع الإجازات الافتراضية
    echo "<p class='info'>📝 إضافة أنواع الإجازات الافتراضية...</p>";
    $leave_types_data = [
        ['إجازة سنوية', 30, 'الإجازة السنوية العادية'],
        ['إجازة مرضية', 15, 'إجازة للحالات المرضية'],
        ['إجازة طارئة', 7, 'إجازة للحالات الطارئة'],
        ['إيفاد', 0, 'إيفاد رسمي للعمل خارج المكتب'],
        ['إجازة أمومة', 90, 'إجازة الأمومة للموظفات'],
        ['إجازة أبوة', 3, 'إجازة الأبوة للموظفين']
    ];

    foreach ($leave_types_data as $type) {
        try {
            $stmt = $conn->prepare("INSERT IGNORE INTO leave_types (name, max_days_per_year, description) VALUES (?, ?, ?)");
            $stmt->execute($type);
        } catch (PDOException $e) {
            // تجاهل الأخطاء للبيانات الموجودة
        }
    }

    // إدراج بيانات تجريبية
    echo "<p class='info'>📝 إضافة بيانات تجريبية...</p>";
    try {
        // بيانات حضور تجريبية
        $stmt = $conn->prepare("INSERT IGNORE INTO attendance (user_id, date, status, check_in_time, check_out_time, notes, created_by) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $attendance_data = [
            [2, date('Y-m-d'), 'حاضر', '08:00:00', '16:00:00', 'حضور منتظم', 1],
            [2, date('Y-m-d', strtotime('-1 day')), 'حاضر', '08:15:00', '16:30:00', 'تأخير بسيط', 1],
            [2, date('Y-m-d', strtotime('-2 days')), 'إجازة', null, null, 'إجازة سنوية', 1]
        ];

        foreach ($attendance_data as $data) {
            $stmt->execute($data);
        }

        // رصيد إجازات تجريبي
        $stmt = $conn->prepare("INSERT IGNORE INTO annual_leave_balance (user_id, year, leave_type_id, total_days, used_days, remaining_days) VALUES (?, ?, ?, ?, ?, ?)");
        $balance_data = [
            [2, date('Y'), 1, 30, 5, 25],
            [2, date('Y'), 2, 15, 2, 13],
            [2, date('Y'), 3, 7, 0, 7]
        ];

        foreach ($balance_data as $data) {
            $stmt->execute($data);
        }

    } catch (PDOException $e) {
        echo "<p class='error'>تحذير: " . $e->getMessage() . "</p>";
    }
    
    echo "<p class='success'>✅ تم تنفيذ $executed_count استعلام بنجاح</p>";
    
    // التحقق من الجداول الجديدة
    $tables_to_check = ['attendance', 'leave_types', 'leave_requests', 'annual_leave_balance'];
    
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM $table");
            $stmt->execute();
            $count = $stmt->fetch()['count'];
            echo "<p class='success'>✅ جدول $table: موجود ($count سجل)</p>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ جدول $table: غير موجود أو به مشكلة</p>";
        }
    }
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📊 الجداول الجديدة:</h3>";
    echo "<ul>";
    echo "<li><strong>attendance:</strong> جدول الحضور والغياب اليومي</li>";
    echo "<li><strong>leave_types:</strong> أنواع الإجازات (سنوية، مرضية، طارئة، إيفاد، إلخ)</li>";
    echo "<li><strong>leave_requests:</strong> طلبات الإجازات من المنتسبين</li>";
    echo "<li><strong>annual_leave_balance:</strong> رصيد الإجازات السنوي لكل منتسب</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎯 المميزات الجديدة:</h3>";
    echo "<ul>";
    echo "<li>تسجيل الحضور والغياب اليومي</li>";
    echo "<li>إدارة طلبات الإجازات</li>";
    echo "<li>تتبع رصيد الإجازات السنوي</li>";
    echo "<li>ربط الحضور بتقييم المنتسبين</li>";
    echo "<li>أنواع متعددة من الإجازات</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p class='success'>🎉 تم التحديث بنجاح!</p>";
    echo "<p><a href='attendance.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📅 إدارة الحضور</a></p>";
    echo "<p><a href='leave_requests.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>📝 طلبات الإجازات</a></p>";
    echo "<p><a href='leave_balance.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>📊 رصيد الإجازات</a></p>";
    echo "<p><a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>🏠 العودة للوحة التحكم</a></p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ في التحديث: " . $e->getMessage() . "</p>";
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>تأكد من:</h3>";
    echo "<ul>";
    echo "<li>تشغيل XAMPP (Apache + MySQL)</li>";
    echo "<li>وجود قاعدة البيانات control_internet_db</li>";
    echo "<li>وجود ملف attendance_tables.sql في نفس المجلد</li>";
    echo "<li>صلاحيات الكتابة في قاعدة البيانات</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><a href='install.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 إعادة تثبيت قاعدة البيانات</a></p>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
