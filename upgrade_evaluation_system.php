<?php
// ملف ترقية شامل لنظام التقييم المتطور
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>ترقية نظام التقييم المتطور</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }";
echo ".container { background: rgba(255,255,255,0.95); color: #333; padding: 40px; border-radius: 15px; box-shadow: 0 0 30px rgba(0,0,0,0.3); max-width: 900px; margin: 0 auto; }";
echo ".success { color: green; padding: 10px; background: #e8f5e8; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: red; padding: 10px; background: #ffe6e6; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: blue; padding: 10px; background: #e8f4fd; border-radius: 5px; margin: 10px 0; }";
echo ".warning { color: orange; padding: 10px; background: #fff3cd; border-radius: 5px; margin: 10px 0; }";
echo ".btn { background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin: 8px; display: inline-block; font-weight: bold; }";
echo ".btn-success { background: #28a745; }";
echo ".btn-warning { background: #ffc107; color: black; }";
echo ".btn-danger { background: #dc3545; }";
echo ".feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }";
echo ".feature-card { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 10px; border-left: 5px solid #667eea; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1 style='text-align: center; color: #667eea; margin-bottom: 30px;'>🚀 ترقية نظام التقييم المتطور</h1>";

try {
    $conn = getDBConnection();
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    $upgrade_steps = [
        'جداول الحضور والإجازات' => false,
        'تحديث جدول التقييمات' => false,
        'إنشاء الفهارس' => false,
        'إنشاء Views المتقدمة' => false,
        'بيانات تجريبية' => false
    ];
    
    // 1. التحقق من جداول الحضور
    echo "<h3>📅 التحقق من جداول الحضور والإجازات...</h3>";
    $required_tables = ['attendance', 'leave_types', 'leave_requests', 'annual_leave_balance'];
    $missing_tables = [];
    
    foreach ($required_tables as $table) {
        try {
            $stmt = $conn->prepare("SELECT 1 FROM $table LIMIT 1");
            $stmt->execute();
            echo "<div class='success'>✅ جدول $table موجود</div>";
        } catch (Exception $e) {
            $missing_tables[] = $table;
            echo "<div class='error'>❌ جدول $table غير موجود</div>";
        }
    }
    
    if (!empty($missing_tables)) {
        echo "<div class='warning'>⚠️ يجب إنشاء جداول الحضور أولاً</div>";
        echo "<p><a href='setup_attendance.php' class='btn btn-warning'>إنشاء جداول الحضور</a></p>";
    } else {
        $upgrade_steps['جداول الحضور والإجازات'] = true;
    }
    
    // 2. تحديث جدول التقييمات
    echo "<h3>⭐ تحديث جدول التقييمات...</h3>";
    
    $new_columns = [
        'task_completion_rate' => "DECIMAL(5,2) DEFAULT 0 COMMENT 'نسبة إنجاز المهام'",
        'attendance_rate' => "DECIMAL(5,2) DEFAULT 0 COMMENT 'نسبة الحضور'",
        'total_tasks' => "INT DEFAULT 0 COMMENT 'إجمالي المهام'",
        'pending_tasks' => "INT DEFAULT 0 COMMENT 'المهام المعلقة'",
        'cancelled_tasks' => "INT DEFAULT 0 COMMENT 'المهام الملغية'",
        'present_days' => "INT DEFAULT 0 COMMENT 'أيام الحضور'",
        'absent_days' => "INT DEFAULT 0 COMMENT 'أيام الغياب'",
        'leave_days' => "INT DEFAULT 0 COMMENT 'أيام الإجازة'",
        'mission_days' => "INT DEFAULT 0 COMMENT 'أيام الإيفاد'",
        'sick_days' => "INT DEFAULT 0 COMMENT 'أيام المرض'",
        'leave_requests_count' => "INT DEFAULT 0 COMMENT 'عدد طلبات الإجازة'",
        'approved_leave_days' => "INT DEFAULT 0 COMMENT 'أيام الإجازة المعتمدة'",
        'rejected_requests' => "INT DEFAULT 0 COMMENT 'الطلبات المرفوضة'",
        'performance_notes' => "TEXT COMMENT 'ملاحظات الأداء التفصيلية'"
    ];
    
    $added_columns = 0;
    foreach ($new_columns as $column_name => $column_definition) {
        try {
            $conn->exec("ALTER TABLE evaluations ADD COLUMN $column_name $column_definition");
            echo "<div class='success'>✅ تم إضافة حقل $column_name</div>";
            $added_columns++;
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<div class='info'>ℹ️ حقل $column_name موجود بالفعل</div>";
                $added_columns++;
            } else {
                echo "<div class='error'>❌ خطأ في إضافة حقل $column_name: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    if ($added_columns >= count($new_columns)) {
        $upgrade_steps['تحديث جدول التقييمات'] = true;
    }
    
    // 3. إنشاء الفهارس
    echo "<h3>🔍 إنشاء فهارس الأداء...</h3>";
    
    $indexes = [
        "CREATE INDEX idx_evaluations_month ON evaluations(month)",
        "CREATE INDEX idx_evaluations_user_month ON evaluations(user_id, month)",
        "CREATE INDEX idx_evaluations_grade ON evaluations(grade)",
        "CREATE INDEX idx_evaluations_total_score ON evaluations(total_score)",
        "CREATE INDEX idx_attendance_user_date ON attendance(user_id, date)",
        "CREATE INDEX idx_leave_requests_user_status ON leave_requests(user_id, status)"
    ];
    
    $created_indexes = 0;
    foreach ($indexes as $index_sql) {
        try {
            $conn->exec($index_sql);
            echo "<div class='success'>✅ تم إنشاء فهرس</div>";
            $created_indexes++;
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<div class='info'>ℹ️ الفهرس موجود بالفعل</div>";
                $created_indexes++;
            } else {
                echo "<div class='error'>❌ خطأ في إنشاء فهرس: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    if ($created_indexes >= count($indexes)) {
        $upgrade_steps['إنشاء الفهارس'] = true;
    }
    
    // 4. إنشاء Views المتقدمة
    echo "<h3>📊 إنشاء Views للتقارير المتقدمة...</h3>";
    
    $views = [
        'evaluation_summary' => "
        CREATE OR REPLACE VIEW evaluation_summary AS
        SELECT 
            e.*,
            u.full_name,
            u.username,
            YEAR(e.month) as evaluation_year,
            MONTH(e.month) as evaluation_month,
            CASE 
                WHEN e.total_score >= 18 THEN 'ممتاز'
                WHEN e.total_score >= 15 THEN 'جيد جداً'
                WHEN e.total_score >= 12 THEN 'جيد'
                WHEN e.total_score >= 10 THEN 'مقبول'
                ELSE 'ضعيف'
            END as calculated_grade,
            (e.quality_score + e.punctuality_score + e.cooperation_score + e.innovation_score) as manual_total,
            COALESCE(e.attendance_rate, 0) as attendance_rate,
            COALESCE(e.task_completion_rate, 0) as task_completion_rate,
            COALESCE(e.present_days + e.leave_days + e.mission_days, 0) as working_days,
            COALESCE(e.absent_days + e.sick_days, 0) as non_working_days
        FROM evaluations e
        JOIN users u ON e.user_id = u.id
        WHERE u.role = 'user'
        ",
        
        'performance_dashboard' => "
        CREATE OR REPLACE VIEW performance_dashboard AS
        SELECT 
            u.id as user_id,
            u.full_name,
            COUNT(e.id) as total_evaluations,
            AVG(e.total_score) as avg_score,
            AVG(COALESCE(e.attendance_rate, 0)) as avg_attendance,
            AVG(COALESCE(e.task_completion_rate, 0)) as avg_task_completion,
            SUM(e.tasks_completed) as total_tasks_completed,
            SUM(e.achievements_count) as total_achievements,
            MAX(e.month) as last_evaluation_month
        FROM users u
        LEFT JOIN evaluations e ON u.id = e.user_id
        WHERE u.role = 'user'
        GROUP BY u.id, u.full_name
        "
    ];
    
    $created_views = 0;
    foreach ($views as $view_name => $view_sql) {
        try {
            $conn->exec($view_sql);
            echo "<div class='success'>✅ تم إنشاء View $view_name</div>";
            $created_views++;
        } catch (PDOException $e) {
            echo "<div class='error'>❌ خطأ في إنشاء View $view_name: " . $e->getMessage() . "</div>";
        }
    }
    
    if ($created_views >= count($views)) {
        $upgrade_steps['إنشاء Views المتقدمة'] = true;
    }
    
    // 5. إضافة بيانات تجريبية (اختياري)
    echo "<h3>🧪 إضافة بيانات تجريبية...</h3>";
    
    try {
        // التحقق من وجود منتسبين
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'user'");
        $stmt->execute();
        $user_count = $stmt->fetch()['count'];
        
        if ($user_count > 0) {
            echo "<div class='success'>✅ يوجد $user_count منتسب في النظام</div>";
            $upgrade_steps['بيانات تجريبية'] = true;
        } else {
            echo "<div class='warning'>⚠️ لا يوجد منتسبين في النظام</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في التحقق من البيانات: " . $e->getMessage() . "</div>";
    }
    
    // عرض ملخص الترقية
    echo "<div style='background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; border-radius: 15px; margin: 40px 0; text-align: center;'>";
    echo "<h2>🎉 ملخص الترقية</h2>";
    
    $completed_steps = array_filter($upgrade_steps);
    $total_steps = count($upgrade_steps);
    $completed_count = count($completed_steps);
    
    echo "<div style='font-size: 2rem; margin: 20px 0;'>";
    echo "$completed_count / $total_steps خطوات مكتملة";
    echo "</div>";
    
    foreach ($upgrade_steps as $step => $completed) {
        $icon = $completed ? '✅' : '❌';
        $color = $completed ? '#28a745' : '#dc3545';
        echo "<div style='margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.2); border-radius: 5px;'>";
        echo "$icon <strong>$step</strong>";
        echo "</div>";
    }
    echo "</div>";
    
    // المميزات الجديدة
    echo "<div style='background: #e8f4fd; padding: 20px; border-radius: 10px; margin: 30px 0;'>";
    echo "<h3 style='color: #0066cc;'>🎯 المميزات الجديدة في نظام التقييم:</h3>";
    
    echo "<div class='feature-grid'>";
    
    echo "<div class='feature-card'>";
    echo "<h4>📊 ربط ذكي بالأداء</h4>";
    echo "<ul>";
    echo "<li>ربط التقييم بنسبة إنجاز المهام</li>";
    echo "<li>ربط التقييم بنسبة الحضور</li>";
    echo "<li>ربط التقييم بعدد الإنجازات</li>";
    echo "<li>ربط التقييم بسلوك استخدام الإجازات</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<h4>🤖 حساب تلقائي للدرجات</h4>";
    echo "<ul>";
    echo "<li>تعديل درجة جودة العمل حسب إنجاز المهام</li>";
    echo "<li>تعديل درجة الالتزام حسب الحضور</li>";
    echo "<li>تعديل درجة الإبداع حسب الإنجازات</li>";
    echo "<li>تعديل درجة التعاون حسب سلوك الإجازات</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<h4>📈 تقارير متقدمة</h4>";
    echo "<ul>";
    echo "<li>إحصائيات شاملة للأداء</li>";
    echo "<li>مقارنات بين المنتسبين</li>";
    echo "<li>تحليل الاتجاهات الزمنية</li>";
    echo "<li>تقارير مرئية تفاعلية</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<h4>📋 ملاحظات تفصيلية</h4>";
    echo "<ul>";
    echo "<li>ملاحظات أداء تلقائية</li>";
    echo "<li>إحصائيات مفصلة لكل تقييم</li>";
    echo "<li>تتبع التحسن عبر الوقت</li>";
    echo "<li>توصيات للتطوير</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
    
    // روابط سريعة
    echo "<div style='text-align: center; margin: 40px 0;'>";
    echo "<h3>🚀 ابدأ الاستخدام:</h3>";
    
    if ($completed_count >= $total_steps - 1) {
        echo "<a href='evaluations.php' class='btn btn-success'>⭐ نظام التقييم المطور</a>";
        echo "<a href='evaluation_reports.php' class='btn btn-success'>📊 التقارير المتقدمة</a>";
        echo "<a href='print_evaluations.php' class='btn btn-success'>🖨️ طباعة التقارير الشاملة</a>";
    } else {
        if (!$upgrade_steps['جداول الحضور والإجازات']) {
            echo "<a href='setup_attendance.php' class='btn btn-warning'>📅 إنشاء جداول الحضور</a>";
        }
        if (!$upgrade_steps['تحديث جدول التقييمات']) {
            echo "<a href='update_evaluations_table.php' class='btn btn-warning'>⭐ تحديث جدول التقييمات</a>";
        }
    }
    
    echo "<a href='dashboard.php' class='btn'>🏠 لوحة التحكم</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في الترقية: " . $e->getMessage() . "</div>";
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>💡 خطوات الإصلاح:</h3>";
    echo "<ol>";
    echo "<li>تأكد من تشغيل XAMPP (Apache + MySQL)</li>";
    echo "<li>تأكد من وجود قاعدة البيانات control_internet_db</li>";
    echo "<li>تأكد من صلاحيات التعديل في قاعدة البيانات</li>";
    echo "<li>قم بتشغيل setup_attendance.php أولاً</li>";
    echo "<li>ثم قم بتشغيل update_evaluations_table.php</li>";
    echo "</ol>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
