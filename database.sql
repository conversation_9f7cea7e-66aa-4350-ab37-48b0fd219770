-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS control_internet_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE control_internet_db;

-- جدول المستخدمين
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'user') DEFAULT 'user',
    full_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المهام اليومية
CREATE TABLE tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    type VARCHAR(100) NOT NULL,
    details TEXT,
    date DATE NOT NULL,
    status ENUM('قيد الإنجاز', 'منجز') DEFAULT 'قيد الإنجاز',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول الإنجازات اليومية
CREATE TABLE achievements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    details TEXT,
    date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- إدراج المستخدم المدير الافتراضي
INSERT INTO users (username, password, role, full_name) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'مدير النظام');
-- كلمة المرور: password

-- إدراج مستخدم تجريبي
INSERT INTO users (username, password, role, full_name) VALUES 
('user1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'موظف تجريبي');
-- كلمة المرور: password

-- إدراج بيانات تجريبية للمهام
INSERT INTO tasks (user_id, title, type, details, date, status) VALUES 
(2, 'صيانة كاميرات المراقبة', 'صيانة', 'فحص وصيانة 10 كاميرات في الطابق الأول', CURDATE(), 'منجز'),
(2, 'متابعة الشبكة', 'مراقبة', 'مراقبة أداء الشبكة وحل المشاكل', CURDATE(), 'قيد الإنجاز'),
(2, 'إصلاح عطل إنترنت', 'إصلاح', 'حل مشكلة انقطاع الإنترنت في القسم الثالث', CURDATE() - INTERVAL 1 DAY, 'منجز');

-- إدراج بيانات تجريبية للإنجازات
INSERT INTO achievements (user_id, title, details, date) VALUES 
(2, 'إكمال صيانة 5 كاميرات', 'تم فحص وصيانة 5 كاميرات بنجاح وإعادة تشغيلها', CURDATE()),
(2, 'حل 3 مشاكل شبكة', 'تم حل مشاكل الاتصال في 3 أقسام مختلفة', CURDATE() - INTERVAL 1 DAY),
(2, 'تحديث نظام الحماية', 'تم تحديث برامج الحماية على جميع الأجهزة', CURDATE() - INTERVAL 2 DAY);
