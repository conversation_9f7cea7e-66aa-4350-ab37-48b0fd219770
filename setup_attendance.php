<?php
// ملف إعداد جداول الحضور والإجازات - مضمون 100%
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>إعداد نظام الحضور والإجازات</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }";
echo ".container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); max-width: 800px; margin: 0 auto; }";
echo ".success { color: green; padding: 10px; background: #e8f5e8; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: red; padding: 10px; background: #ffe6e6; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: blue; padding: 10px; background: #e8f4fd; border-radius: 5px; margin: 10px 0; }";
echo ".btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block; }";
echo ".btn-success { background: #28a745; }";
echo ".btn-warning { background: #ffc107; color: black; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🚀 إعداد نظام الحضور والإجازات</h1>";

try {
    $conn = getDBConnection();
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // إنشاء جدول الحضور
    echo "<h3>📅 إنشاء جدول الحضور...</h3>";
    $attendance_sql = "CREATE TABLE IF NOT EXISTS attendance (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        date DATE NOT NULL,
        status ENUM('حاضر', 'غائب', 'إجازة', 'إيفاد', 'مرض') NOT NULL DEFAULT 'حاضر',
        check_in_time TIME NULL,
        check_out_time TIME NULL,
        notes TEXT,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_date (user_id, date),
        INDEX idx_user_date (user_id, date),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($attendance_sql);
    echo "<div class='success'>✅ تم إنشاء جدول الحضور</div>";
    
    // إنشاء جدول أنواع الإجازات
    echo "<h3>📋 إنشاء جدول أنواع الإجازات...</h3>";
    $leave_types_sql = "CREATE TABLE IF NOT EXISTS leave_types (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        max_days_per_year INT DEFAULT 30,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($leave_types_sql);
    echo "<div class='success'>✅ تم إنشاء جدول أنواع الإجازات</div>";
    
    // إنشاء جدول طلبات الإجازات
    echo "<h3>📝 إنشاء جدول طلبات الإجازات...</h3>";
    $leave_requests_sql = "CREATE TABLE IF NOT EXISTS leave_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        leave_type_id INT NOT NULL,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        days_count INT NOT NULL,
        reason TEXT,
        status ENUM('معلق', 'موافق', 'مرفوض') DEFAULT 'معلق',
        approved_by INT NULL,
        approved_at TIMESTAMP NULL,
        rejection_reason TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_status (user_id, status),
        INDEX idx_dates (start_date, end_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($leave_requests_sql);
    echo "<div class='success'>✅ تم إنشاء جدول طلبات الإجازات</div>";
    
    // إنشاء جدول رصيد الإجازات
    echo "<h3>💰 إنشاء جدول رصيد الإجازات...</h3>";
    $balance_sql = "CREATE TABLE IF NOT EXISTS annual_leave_balance (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        year YEAR NOT NULL,
        leave_type_id INT NOT NULL,
        total_days INT DEFAULT 30,
        used_days INT DEFAULT 0,
        remaining_days INT DEFAULT 30,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_year_type (user_id, year, leave_type_id),
        INDEX idx_user_year (user_id, year)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($balance_sql);
    echo "<div class='success'>✅ تم إنشاء جدول رصيد الإجازات</div>";
    
    // إضافة أنواع الإجازات الافتراضية
    echo "<h3>📊 إضافة أنواع الإجازات الافتراضية...</h3>";
    $leave_types = [
        ['إجازة سنوية', 30, 'الإجازة السنوية العادية'],
        ['إجازة مرضية', 15, 'إجازة للحالات المرضية'],
        ['إجازة طارئة', 7, 'إجازة للحالات الطارئة'],
        ['إيفاد', 0, 'إيفاد رسمي للعمل خارج المكتب'],
        ['إجازة أمومة', 90, 'إجازة الأمومة للموظفات'],
        ['إجازة أبوة', 3, 'إجازة الأبوة للموظفين']
    ];
    
    $stmt = $conn->prepare("INSERT IGNORE INTO leave_types (name, max_days_per_year, description) VALUES (?, ?, ?)");
    foreach ($leave_types as $type) {
        $stmt->execute($type);
    }
    echo "<div class='success'>✅ تم إضافة أنواع الإجازات</div>";
    
    // إضافة بيانات تجريبية
    echo "<h3>🧪 إضافة بيانات تجريبية...</h3>";
    
    // التحقق من وجود منتسبين
    $stmt = $conn->prepare("SELECT id FROM users WHERE role = 'user' LIMIT 1");
    $stmt->execute();
    $test_user = $stmt->fetch();
    
    if ($test_user) {
        $user_id = $test_user['id'];
        
        // إضافة بيانات حضور تجريبية
        $stmt = $conn->prepare("INSERT IGNORE INTO attendance (user_id, date, status, check_in_time, check_out_time, notes, created_by) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $attendance_data = [
            [$user_id, date('Y-m-d'), 'حاضر', '08:00:00', '16:00:00', 'حضور منتظم', 1],
            [$user_id, date('Y-m-d', strtotime('-1 day')), 'حاضر', '08:15:00', '16:30:00', 'تأخير بسيط', 1],
            [$user_id, date('Y-m-d', strtotime('-2 days')), 'إجازة', null, null, 'إجازة سنوية', 1]
        ];
        
        foreach ($attendance_data as $data) {
            $stmt->execute($data);
        }
        
        // إضافة رصيد إجازات تجريبي
        $stmt = $conn->prepare("INSERT IGNORE INTO annual_leave_balance (user_id, year, leave_type_id, total_days, used_days, remaining_days) VALUES (?, ?, ?, ?, ?, ?)");
        $balance_data = [
            [$user_id, date('Y'), 1, 30, 5, 25],
            [$user_id, date('Y'), 2, 15, 2, 13],
            [$user_id, date('Y'), 3, 7, 0, 7]
        ];
        
        foreach ($balance_data as $data) {
            $stmt->execute($data);
        }
        
        echo "<div class='success'>✅ تم إضافة بيانات تجريبية</div>";
    } else {
        echo "<div class='info'>ℹ️ لا يوجد منتسبين لإضافة بيانات تجريبية</div>";
    }
    
    // التحقق من الجداول
    echo "<h3>🔍 التحقق من الجداول...</h3>";
    $tables = ['attendance', 'leave_types', 'leave_requests', 'annual_leave_balance'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM $table");
            $stmt->execute();
            $count = $stmt->fetch()['count'];
            echo "<div class='success'>✅ جدول $table: موجود ($count سجل)</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ جدول $table: مشكلة - " . $e->getMessage() . "</div>";
        }
    }
    
    echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 30px 0;'>";
    echo "<h2>🎉 تم الإعداد بنجاح!</h2>";
    echo "<p><strong>الجداول المنشأة:</strong></p>";
    echo "<ul>";
    echo "<li>📅 <strong>attendance:</strong> جدول الحضور والغياب اليومي</li>";
    echo "<li>📋 <strong>leave_types:</strong> أنواع الإجازات</li>";
    echo "<li>📝 <strong>leave_requests:</strong> طلبات الإجازات</li>";
    echo "<li>💰 <strong>annual_leave_balance:</strong> رصيد الإجازات السنوي</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<h3>🚀 ابدأ الآن:</h3>";
    echo "<a href='attendance.php' class='btn btn-success'>📅 إدارة الحضور</a>";
    echo "<a href='leave_requests.php' class='btn btn-success'>📝 طلبات الإجازات</a>";
    echo "<a href='leave_balance.php' class='btn btn-warning'>💰 رصيد الإجازات</a>";
    echo "<a href='dashboard.php' class='btn'>🏠 لوحة التحكم</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في الإعداد: " . $e->getMessage() . "</div>";
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>💡 تأكد من:</h3>";
    echo "<ul>";
    echo "<li>تشغيل XAMPP (Apache + MySQL)</li>";
    echo "<li>وجود قاعدة البيانات control_internet_db</li>";
    echo "<li>صلاحيات الكتابة في قاعدة البيانات</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><a href='install.php' class='btn btn-warning'>🔧 إعادة تثبيت قاعدة البيانات</a></p>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
