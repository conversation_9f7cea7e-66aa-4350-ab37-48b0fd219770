# نظام إدارة شعبة السيطرة والإنترنت

نظام شامل لإدارة الأعمال اليومية والإنجازات لشعبة السيطرة والإنترنت، مطور بـ PHP و MySQL ويعمل على XAMPP.

## المميزات

### 🔐 نظام تسجيل الدخول
- حساب مدير (admin) لإدارة النظام
- حسابات منتسبين عاديين
- حماية الجلسات (Sessions)

### 📋 إدارة المهام اليومية
- إضافة وتعديل وحذف المهام
- تصنيف المهام حسب النوع (صيانة، مراقبة، إصلاح، تطوير، تدريب)
- تتبع حالة المهام (قيد الإنجاز/منجز)
- عرض المهام حسب المستخدم أو جميع المستخدمين للمدير

### 🏆 إدارة الإنجازات
- تسجيل الإنجازات اليومية
- تعديل وحذف الإنجازات
- عرض الإنجازات بشكل بطاقات تفاعلية

### 📊 لوحة التحكم
- إحصائيات سريعة (عدد المهام، المهام المكتملة، الإنجازات)
- عرض المهام والإنجازات الحديثة
- أعمال سريعة للوصول المباشر

### 📈 التقارير
- تقارير شهرية مفصلة
- تقارير يومية
- إمكانية الطباعة
- فلترة حسب المنتسب (للمدير)
- إحصائيات شاملة

### 👥 نظام إدارة المنتسبين
- إضافة وتعديل وحذف المنتسبين (للمدير فقط)
- إنشاء حسابات جديدة مع كلمات مرور
- إضافة منتسبين بالجملة من قائمة
- إعادة تعيين كلمات المرور
- إحصائيات شاملة لأداء المنتسبين

### ⭐ نظام تقييم المنتسبين
- تقييم شهري للمنتسبين (للمدير فقط)
- معايير تقييم متعددة (جودة العمل، الالتزام، التعاون، الإبداع)
- حساب تلقائي للدرجات والتقديرات
- تتبع المهام المنجزة والإنجازات
- ملاحظات وتوصيات للتطوير

### 🎨 واجهة المستخدم
- تصميم عصري باستخدام Bootstrap 5
- دعم كامل للغة العربية (RTL)
- واجهة سهلة ومتجاوبة
- ألوان وتأثيرات جذابة

## متطلبات النظام

- XAMPP (Apache + MySQL + PHP 7.4+)
- متصفح ويب حديث

## التثبيت

### 1. تحضير البيئة
```bash
# تأكد من تشغيل XAMPP
# ابدأ تشغيل Apache و MySQL من لوحة تحكم XAMPP
```

### 2. نسخ الملفات
```bash
# انسخ جميع ملفات المشروع إلى مجلد htdocs في XAMPP
# المسار: C:\xampp\htdocs\control_internet_system\
```

### 3. إنشاء قاعدة البيانات
1. افتح phpMyAdmin من خلال: `http://localhost/phpmyadmin`
2. أنشئ قاعدة بيانات جديدة باسم: `control_internet_db`
3. استورد ملف `database.sql` أو نفذ الأوامر الموجودة فيه

### 4. تشغيل النظام
افتح المتصفح وانتقل إلى: `http://localhost/control_internet_system/`

## الحسابات الافتراضية

### حساب المدير
- **اسم المستخدم:** admin
- **كلمة المرور:** password

### حساب منتسب تجريبي
- **اسم المستخدم:** user1
- **كلمة المرور:** password

## هيكل المشروع

```
control_internet_system/
├── config/
│   └── database.php          # إعدادات قاعدة البيانات
├── includes/
│   ├── auth.php             # نظام المصادقة
│   ├── header.php           # رأس الصفحة
│   └── footer.php           # تذييل الصفحة
├── database.sql             # هيكل قاعدة البيانات
├── index.php               # الصفحة الرئيسية
├── login.php               # تسجيل الدخول
├── logout.php              # تسجيل الخروج
├── dashboard.php           # لوحة التحكم
├── tasks.php               # إدارة المهام
├── achievements.php        # إدارة الإنجازات
├── reports.php             # التقارير
└── README.md               # هذا الملف
```

## قاعدة البيانات

### جدول المستخدمين (users)
- `id` - المعرف الفريد
- `username` - اسم المستخدم
- `password` - كلمة المرور المشفرة
- `role` - الدور (admin/user)
- `full_name` - الاسم الكامل
- `created_at` - تاريخ الإنشاء

### جدول المهام (tasks)
- `id` - المعرف الفريد
- `user_id` - معرف المستخدم
- `title` - عنوان المهمة
- `type` - نوع العمل
- `details` - تفاصيل المهمة
- `date` - تاريخ المهمة
- `status` - الحالة
- `created_at` - تاريخ الإنشاء
- `updated_at` - تاريخ التحديث

### جدول الإنجازات (achievements)
- `id` - المعرف الفريد
- `user_id` - معرف المستخدم
- `title` - عنوان الإنجاز
- `details` - تفاصيل الإنجاز
- `date` - تاريخ الإنجاز
- `created_at` - تاريخ الإنشاء
- `updated_at` - تاريخ التحديث

## الاستخدام

### للموظفين العاديين:
1. تسجيل الدخول بالحساب المخصص
2. إضافة المهام اليومية
3. تسجيل الإنجازات
4. عرض التقارير الشخصية

### للمدير:
1. تسجيل الدخول بحساب المدير
2. عرض جميع المهام والإنجازات
3. إنشاء تقارير شاملة
4. مراقبة أداء الموظفين

## الأمان

- تشفير كلمات المرور باستخدام `password_hash()`
- حماية من SQL Injection باستخدام Prepared Statements
- التحقق من الجلسات في جميع الصفحات
- التحقق من الصلاحيات قبل العمليات الحساسة

## التخصيص

يمكنك تخصيص النظام من خلال:
- تعديل الألوان في ملف `includes/header.php`
- إضافة أنواع مهام جديدة في `tasks.php`
- تخصيص التقارير في `reports.php`
- تعديل إعدادات قاعدة البيانات في `config/database.php`

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---

**تم تطوير هذا النظام خصيصاً لشعبة السيطرة والإنترنت لتسهيل إدارة الأعمال اليومية وتتبع الإنجازات.**
