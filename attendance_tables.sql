-- جدول الحضور والغياب
CREATE TABLE IF NOT EXISTS attendance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    date DATE NOT NULL,
    status ENUM('حاضر', 'غائ<PERSON>', 'إجازة', 'إيفاد', 'مرض') NOT NULL DEFAULT 'حاضر',
    check_in_time TIME NULL,
    check_out_time TIME NULL,
    notes TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    UNIQUE KEY unique_user_date (user_id, date)
);

-- جدول أنواع الإجازات
CREATE TABLE IF NOT EXISTS leave_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    max_days_per_year INT DEFAULT 30,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول طلبات الإجازات
CREATE TABLE IF NOT EXISTS leave_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    leave_type_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    days_count INT NOT NULL,
    reason TEXT,
    status ENUM('معلق', 'موافق', 'مرفوض') DEFAULT 'معلق',
    approved_by INT NULL,
    approved_at TIMESTAMP NULL,
    rejection_reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (leave_type_id) REFERENCES leave_types(id),
    FOREIGN KEY (approved_by) REFERENCES users(id)
);

-- جدول رصيد الإجازات السنوي
CREATE TABLE IF NOT EXISTS annual_leave_balance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    year YEAR NOT NULL,
    leave_type_id INT NOT NULL,
    total_days INT DEFAULT 30,
    used_days INT DEFAULT 0,
    remaining_days INT DEFAULT 30,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (leave_type_id) REFERENCES leave_types(id),
    UNIQUE KEY unique_user_year_type (user_id, year, leave_type_id)
);

-- إدراج أنواع الإجازات الافتراضية
INSERT IGNORE INTO leave_types (name, max_days_per_year, description) VALUES 
('إجازة سنوية', 30, 'الإجازة السنوية العادية'),
('إجازة مرضية', 15, 'إجازة للحالات المرضية'),
('إجازة طارئة', 7, 'إجازة للحالات الطارئة'),
('إيفاد', 0, 'إيفاد رسمي للعمل خارج المكتب'),
('إجازة أمومة', 90, 'إجازة الأمومة للموظفات'),
('إجازة أبوة', 3, 'إجازة الأبوة للموظفين');

-- إدراج بيانات تجريبية للحضور
INSERT IGNORE INTO attendance (user_id, date, status, check_in_time, check_out_time, notes, created_by) VALUES 
(2, CURDATE(), 'حاضر', '08:00:00', '16:00:00', 'حضور منتظم', 1),
(2, CURDATE() - INTERVAL 1 DAY, 'حاضر', '08:15:00', '16:30:00', 'تأخير بسيط', 1),
(2, CURDATE() - INTERVAL 2 DAY, 'إجازة', NULL, NULL, 'إجازة سنوية', 1),
(2, CURDATE() - INTERVAL 3 DAY, 'حاضر', '07:45:00', '16:00:00', 'حضور مبكر', 1);

-- إنشاء رصيد إجازات تجريبي للسنة الحالية
INSERT IGNORE INTO annual_leave_balance (user_id, year, leave_type_id, total_days, used_days, remaining_days) VALUES 
(2, YEAR(CURDATE()), 1, 30, 5, 25),
(2, YEAR(CURDATE()), 2, 15, 2, 13),
(2, YEAR(CURDATE()), 3, 7, 0, 7);

-- إدراج طلب إجازة تجريبي
INSERT IGNORE INTO leave_requests (user_id, leave_type_id, start_date, end_date, days_count, reason, status, approved_by, approved_at) VALUES 
(2, 1, CURDATE() + INTERVAL 7 DAY, CURDATE() + INTERVAL 9 DAY, 3, 'إجازة شخصية', 'موافق', 1, NOW());
