<?php
// ملف إصلاح سريع لمشكلة الدالة المكررة
echo "<h2>🔧 إصلاح مشكلة الدالة المكررة</h2>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;}</style>";

try {
    // قراءة محتوى ملف auth.php
    $auth_content = file_get_contents('includes/auth.php');
    
    if ($auth_content === false) {
        throw new Exception("لا يمكن قراءة ملف auth.php");
    }
    
    // إنشاء محتوى جديد صحيح
    $new_content = '<?php
session_start();

// التحقق من تسجيل الدخول
function checkLogin() {
    if (!isset($_SESSION[\'user_id\'])) {
        header(\'Location: login.php\');
        exit();
    }
}

// التحقق من صلاحيات المدير
function checkAdmin() {
    checkLogin();
    if ($_SESSION[\'role\'] !== \'admin\') {
        $_SESSION[\'error\'] = \'ليس لديك صلاحية للوصول لهذه الصفحة\';
        header(\'Location: user_dashboard.php\');
        exit();
    }
}

// الحصول على بيانات المستخدم الحالي
function getCurrentUser() {
    if (!isset($_SESSION[\'user_id\'])) {
        return null;
    }
    
    return [
        \'id\' => $_SESSION[\'user_id\'],
        \'username\' => $_SESSION[\'username\'],
        \'role\' => $_SESSION[\'role\'],
        \'full_name\' => $_SESSION[\'full_name\']
    ];
}

// التحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// تشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}
?>';
    
    // كتابة المحتوى الجديد
    $result = file_put_contents('includes/auth.php', $new_content);
    
    if ($result === false) {
        throw new Exception("لا يمكن كتابة ملف auth.php");
    }
    
    echo "<p class='success'>✅ تم إصلاح ملف auth.php بنجاح</p>";
    echo "<p class='success'>✅ تم حذف الدالة المكررة</p>";
    echo "<p class='success'>✅ النظام جاهز للاستخدام</p>";
    
    echo "<hr>";
    echo "<h3>🚀 الخطوات التالية:</h3>";
    echo "<p><a href='update_attendance_db.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>1. تحديث قاعدة البيانات للحضور</a></p>";
    echo "<p><a href='login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 10px;'>2. تسجيل الدخول</a></p>";
    echo "<p><a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 10px;'>3. لوحة التحكم</a></p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ خطأ: " . $e->getMessage() . "</p>";
    
    echo "<h3>الحل اليدوي:</h3>";
    echo "<p>1. احذف ملف includes/auth.php</p>";
    echo "<p>2. أنشئ ملف جديد بنفس الاسم</p>";
    echo "<p>3. انسخ المحتوى الصحيح من أعلى</p>";
}
?>
