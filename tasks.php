<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkLogin();

$user = getCurrentUser();
$conn = getDBConnection();
$action = $_GET['action'] ?? 'list';
$task_id = $_GET['id'] ?? null;

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $post_action = $_POST['action'] ?? '';
    
    if ($post_action === 'add') {
        $title = trim($_POST['title'] ?? '');
        $type = trim($_POST['type'] ?? '');
        $details = trim($_POST['details'] ?? '');
        $date = $_POST['date'] ?? date('Y-m-d');
        $status = $_POST['status'] ?? 'قيد الإنجاز';
        
        if (!empty($title) && !empty($type)) {
            try {
                $stmt = $conn->prepare("INSERT INTO tasks (user_id, title, type, details, date, status) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->execute([$user['id'], $title, $type, $details, $date, $status]);
                $_SESSION['success'] = 'تم إضافة المهمة بنجاح';
                header('Location: tasks.php');
                exit();
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء إضافة المهمة';
            }
        } else {
            $_SESSION['error'] = 'يرجى ملء جميع الحقول المطلوبة';
        }
    }
    
    elseif ($post_action === 'edit') {
        $id = $_POST['id'] ?? '';
        $title = trim($_POST['title'] ?? '');
        $type = trim($_POST['type'] ?? '');
        $details = trim($_POST['details'] ?? '');
        $date = $_POST['date'] ?? '';
        $status = $_POST['status'] ?? '';
        
        if (!empty($id) && !empty($title) && !empty($type)) {
            try {
                // التحقق من ملكية المهمة أو صلاحيات المدير
                $where_condition = $user['role'] === 'admin' ? 'id = ?' : 'id = ? AND user_id = ?';
                $params = $user['role'] === 'admin' ? [$title, $type, $details, $date, $status, $id] : [$title, $type, $details, $date, $status, $id, $user['id']];
                
                $stmt = $conn->prepare("UPDATE tasks SET title = ?, type = ?, details = ?, date = ?, status = ? WHERE $where_condition");
                $stmt->execute($params);
                
                if ($stmt->rowCount() > 0) {
                    $_SESSION['success'] = 'تم تحديث المهمة بنجاح';
                } else {
                    $_SESSION['error'] = 'لم يتم العثور على المهمة أو ليس لديك صلاحية لتعديلها';
                }
                header('Location: tasks.php');
                exit();
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء تحديث المهمة';
            }
        } else {
            $_SESSION['error'] = 'يرجى ملء جميع الحقول المطلوبة';
        }
    }
    
    elseif ($post_action === 'delete') {
        $id = $_POST['id'] ?? '';
        
        if (!empty($id)) {
            try {
                $where_condition = $user['role'] === 'admin' ? 'id = ?' : 'id = ? AND user_id = ?';
                $params = $user['role'] === 'admin' ? [$id] : [$id, $user['id']];
                
                $stmt = $conn->prepare("DELETE FROM tasks WHERE $where_condition");
                $stmt->execute($params);
                
                if ($stmt->rowCount() > 0) {
                    $_SESSION['success'] = 'تم حذف المهمة بنجاح';
                } else {
                    $_SESSION['error'] = 'لم يتم العثور على المهمة أو ليس لديك صلاحية لحذفها';
                }
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء حذف المهمة';
            }
        }
        header('Location: tasks.php');
        exit();
    }
    
    elseif ($post_action === 'update_status') {
        $id = $_POST['task_id'] ?? '';
        $status = $_POST['status'] ?? '';
        
        if (!empty($id) && !empty($status)) {
            try {
                $where_condition = $user['role'] === 'admin' ? 'id = ?' : 'id = ? AND user_id = ?';
                $params = $user['role'] === 'admin' ? [$status, $id] : [$status, $id, $user['id']];
                
                $stmt = $conn->prepare("UPDATE tasks SET status = ? WHERE $where_condition");
                $stmt->execute($params);
                
                if ($stmt->rowCount() > 0) {
                    $_SESSION['success'] = 'تم تحديث حالة المهمة بنجاح';
                } else {
                    $_SESSION['error'] = 'لم يتم العثور على المهمة';
                }
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء تحديث الحالة';
            }
        }
        header('Location: tasks.php');
        exit();
    }
}

// جلب المهام
if ($user['role'] === 'admin') {
    $stmt = $conn->prepare("
        SELECT t.*, u.full_name
        FROM tasks t
        JOIN users u ON t.user_id = u.id
        ORDER BY t.date DESC, t.created_at DESC
    ");
    $stmt->execute();
} else {
    $stmt = $conn->prepare("
        SELECT t.*, u.full_name
        FROM tasks t
        JOIN users u ON t.user_id = u.id
        WHERE t.user_id = ?
        ORDER BY t.date DESC, t.created_at DESC
    ");
    $stmt->execute([$user['id']]);
}
$tasks = $stmt->fetchAll();

// جلب مهمة للتعديل
$edit_task = null;
if ($action === 'edit' && $task_id) {
    $where_condition = $user['role'] === 'admin' ? 'id = ?' : 'id = ? AND user_id = ?';
    $params = $user['role'] === 'admin' ? [$task_id] : [$task_id, $user['id']];
    
    $stmt = $conn->prepare("SELECT * FROM tasks WHERE $where_condition");
    $stmt->execute($params);
    $edit_task = $stmt->fetch();
    
    if (!$edit_task) {
        $_SESSION['error'] = 'لم يتم العثور على المهمة';
        header('Location: tasks.php');
        exit();
    }
}

$page_title = 'إدارة المهام اليومية';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-tasks me-2"></i>
                إدارة المهام اليومية
            </h1>
            <?php if ($action !== 'add' && $action !== 'edit'): ?>
                <a href="tasks.php?action=add" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مهمة جديدة
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php if ($action === 'add' || $action === 'edit'): ?>
<!-- نموذج إضافة/تعديل المهمة -->
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?> me-2"></i>
                <?php echo $action === 'add' ? 'إضافة مهمة جديدة' : 'تعديل المهمة'; ?>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="action" value="<?php echo $action; ?>">
                    <?php if ($action === 'edit'): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_task['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="title" class="form-label">عنوان المهمة *</label>
                            <input type="text" class="form-control" id="title" name="title" 
                                   value="<?php echo htmlspecialchars($edit_task['title'] ?? ''); ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="type" class="form-label">نوع العمل *</label>
                            <select class="form-select" id="type" name="type" required>
                                <option value="">اختر نوع العمل</option>
                                <option value="صيانة" <?php echo ($edit_task['type'] ?? '') === 'صيانة' ? 'selected' : ''; ?>>صيانة</option>
                                <option value="مراقبة" <?php echo ($edit_task['type'] ?? '') === 'مراقبة' ? 'selected' : ''; ?>>مراقبة</option>
                                <option value="إصلاح" <?php echo ($edit_task['type'] ?? '') === 'إصلاح' ? 'selected' : ''; ?>>إصلاح</option>
                                <option value="تطوير" <?php echo ($edit_task['type'] ?? '') === 'تطوير' ? 'selected' : ''; ?>>تطوير</option>
                                <option value="تدريب" <?php echo ($edit_task['type'] ?? '') === 'تدريب' ? 'selected' : ''; ?>>تدريب</option>
                                <option value="أخرى" <?php echo ($edit_task['type'] ?? '') === 'أخرى' ? 'selected' : ''; ?>>أخرى</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date" class="form-label">التاريخ *</label>
                            <input type="date" class="form-control" id="date" name="date" 
                                   value="<?php echo $edit_task['date'] ?? date('Y-m-d'); ?>" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="قيد الإنجاز" <?php echo ($edit_task['status'] ?? 'قيد الإنجاز') === 'قيد الإنجاز' ? 'selected' : ''; ?>>قيد الإنجاز</option>
                                <option value="منجز" <?php echo ($edit_task['status'] ?? '') === 'منجز' ? 'selected' : ''; ?>>منجز</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="details" class="form-label">تفاصيل المهمة</label>
                        <textarea class="form-control" id="details" name="details" rows="4" 
                                  placeholder="اكتب تفاصيل المهمة هنا..."><?php echo htmlspecialchars($edit_task['details'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            <?php echo $action === 'add' ? 'إضافة المهمة' : 'حفظ التغييرات'; ?>
                        </button>
                        <a href="tasks.php" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php else: ?>
<!-- قائمة المهام -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>
                    <i class="fas fa-list me-2"></i>
                    قائمة المهام (<?php echo count($tasks); ?> مهمة)
                </span>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="window.print()">
                        <i class="fas fa-print me-1"></i>
                        طباعة
                    </button>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($tasks)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد مهام مسجلة</h5>
                        <p class="text-muted">ابدأ بإضافة مهمة جديدة</p>
                        <a href="tasks.php?action=add" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مهمة جديدة
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>عنوان المهمة</th>
                                    <th>نوع العمل</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                    <?php if ($user['role'] === 'admin'): ?>
                                        <th>المنتسب</th>
                                    <?php endif; ?>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tasks as $task): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($task['title']); ?></strong>
                                            <?php if (!empty($task['details'])): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars(substr($task['details'], 0, 50)) . '...'; ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo htmlspecialchars($task['type']); ?></span>
                                        </td>
                                        <td><?php echo date('Y-m-d', strtotime($task['date'])); ?></td>
                                        <td>
                                            <span class="badge <?php echo $task['status'] === 'منجز' ? 'bg-success' : 'bg-warning'; ?>">
                                                <?php echo $task['status']; ?>
                                            </span>
                                        </td>
                                        <?php if ($user['role'] === 'admin'): ?>
                                            <td><?php echo htmlspecialchars($task['full_name']); ?></td>
                                        <?php endif; ?>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <?php if ($task['status'] === 'قيد الإنجاز'): ?>
                                                    <button class="btn btn-success" onclick="updateStatus(<?php echo $task['id']; ?>, 'منجز')" title="تحديد كمنجز">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <button class="btn btn-warning" onclick="updateStatus(<?php echo $task['id']; ?>, 'قيد الإنجاز')" title="تحديد كقيد الإنجاز">
                                                        <i class="fas fa-clock"></i>
                                                    </button>
                                                <?php endif; ?>
                                                
                                                <a href="tasks.php?action=edit&id=<?php echo $task['id']; ?>" class="btn btn-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                
                                                <form method="POST" style="display: inline;" onsubmit="return confirmDelete('هل أنت متأكد من حذف هذه المهمة؟')">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="id" value="<?php echo $task['id']; ?>">
                                                    <button type="submit" class="btn btn-danger" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
