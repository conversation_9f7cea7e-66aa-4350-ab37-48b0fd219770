<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkLogin();

$user = getCurrentUser();
$conn = getDBConnection();
$action = $_GET['action'] ?? 'list';
$request_id = $_GET['id'] ?? null;

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $post_action = $_POST['action'] ?? '';
    
    if ($post_action === 'add') {
        $leave_type_id = $_POST['leave_type_id'] ?? '';
        $start_date = $_POST['start_date'] ?? '';
        $end_date = $_POST['end_date'] ?? '';
        $reason = trim($_POST['reason'] ?? '');
        $is_hourly = isset($_POST['is_hourly']) ? 1 : 0;
        $hours_count = floatval($_POST['hours_count'] ?? 0);
        $start_time = $_POST['start_time'] ?? null;
        $end_time = $_POST['end_time'] ?? null;
        $emergency_type = trim($_POST['emergency_type'] ?? '');
        $priority = $_POST['priority'] ?? 'عادي';

        if (!empty($leave_type_id)) {
            // جلب معلومات نوع الإجازة
            $stmt = $conn->prepare("SELECT * FROM leave_types WHERE id = ?");
            $stmt->execute([$leave_type_id]);
            $leave_type = $stmt->fetch();

            if (!$leave_type) {
                $_SESSION['error'] = 'نوع الإجازة غير صحيح';
            } else {
                $days_count = 0;

                if ($is_hourly && $leave_type['can_be_hourly']) {
                    // إجازة بالساعة
                    if ($hours_count > 0 && !empty($start_time) && !empty($end_time)) {
                        $days_count = 0; // للإجازات بالساعة
                    } else {
                        $_SESSION['error'] = 'يرجى تحديد عدد الساعات ووقت البداية والنهاية';
                    }
                } else {
                    // إجازة بالأيام
                    if (!empty($start_date) && !empty($end_date)) {
                        $start = new DateTime($start_date);
                        $end = new DateTime($end_date);
                        $days_count = $end->diff($start)->days + 1;
                        $hours_count = 0;
                        $start_time = null;
                        $end_time = null;
                    } else {
                        $_SESSION['error'] = 'يرجى تحديد تاريخ البداية والنهاية';
                    }
                }

                if (!isset($_SESSION['error'])) {
                    // التحقق من الرصيد للإجازات القابلة للاستقطاع
                    if ($leave_type['is_deductible'] && !$is_hourly) {
                        $stmt = $conn->prepare("
                            SELECT remaining_days
                            FROM annual_leave_balance
                            WHERE user_id = ? AND year = ? AND leave_type_id = ?
                        ");
                        $stmt->execute([$user['id'], date('Y'), $leave_type_id]);
                        $balance = $stmt->fetch();

                        if (!$balance || $balance['remaining_days'] < $days_count) {
                            $_SESSION['error'] = 'رصيد الإجازة غير كافي. الرصيد المتاح: ' . ($balance['remaining_days'] ?? 0) . ' يوم';
                        }
                    }

                    if (!isset($_SESSION['error'])) {
                        try {
                            $stmt = $conn->prepare("
                                INSERT INTO leave_requests (
                                    user_id, leave_type_id, start_date, end_date, days_count, reason,
                                    is_hourly, hours_count, start_time, end_time, emergency_type, priority
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ");
                            $stmt->execute([
                                $user['id'], $leave_type_id, $start_date, $end_date, $days_count, $reason,
                                $is_hourly, $hours_count, $start_time, $end_time, $emergency_type, $priority
                            ]);
                            $_SESSION['success'] = 'تم تقديم طلب الإجازة بنجاح';
                            header('Location: leave_requests.php');
                            exit();
                        } catch (Exception $e) {
                            $_SESSION['error'] = 'حدث خطأ أثناء تقديم الطلب: ' . $e->getMessage();
                        }
                    }
                }
            }
        } else {
            $_SESSION['error'] = 'يرجى اختيار نوع الإجازة';
        }
    }
    
    elseif ($post_action === 'approve' && $user['role'] === 'admin') {
        $id = $_POST['id'] ?? '';
        if (!empty($id)) {
            try {
                $conn->beginTransaction();

                // الحصول على تفاصيل الطلب ونوع الإجازة
                $stmt = $conn->prepare("
                    SELECT lr.*, lt.is_deductible, lt.can_be_hourly, lt.name as leave_type_name
                    FROM leave_requests lr
                    JOIN leave_types lt ON lr.leave_type_id = lt.id
                    WHERE lr.id = ?
                ");
                $stmt->execute([$id]);
                $request = $stmt->fetch();

                if ($request) {
                    // تحديث حالة الطلب
                    $stmt = $conn->prepare("UPDATE leave_requests SET status = 'موافق', approved_by = ?, approved_at = NOW() WHERE id = ?");
                    $stmt->execute([$user['id'], $id]);

                    // تحديث رصيد الإجازات (فقط للإجازات القابلة للاستقطاع)
                    if ($request['is_deductible']) {
                        if ($request['is_hourly']) {
                            // تحديث رصيد الساعات
                            $stmt = $conn->prepare("
                                UPDATE annual_leave_balance
                                SET used_hours = used_hours + ?, remaining_hours = remaining_hours - ?
                                WHERE user_id = ? AND year = ? AND leave_type_id = ?
                            ");
                            $stmt->execute([$request['hours_count'], $request['hours_count'], $request['user_id'], date('Y'), $request['leave_type_id']]);

                            // إضافة سجل في جدول الساعات
                            $stmt = $conn->prepare("
                                INSERT INTO hourly_leave_log (user_id, leave_request_id, date, start_time, end_time, hours_used, reason, approved_by)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                            ");
                            $stmt->execute([
                                $request['user_id'], $request['id'], $request['start_date'],
                                $request['start_time'], $request['end_time'], $request['hours_count'],
                                $request['reason'], $user['id']
                            ]);
                        } else {
                            // تحديث رصيد الأيام
                            $stmt = $conn->prepare("
                                UPDATE annual_leave_balance
                                SET used_days = used_days + ?, remaining_days = remaining_days - ?
                                WHERE user_id = ? AND year = ? AND leave_type_id = ?
                            ");
                            $stmt->execute([$request['days_count'], $request['days_count'], $request['user_id'], date('Y'), $request['leave_type_id']]);
                        }
                    }

                    // إضافة سجلات الحضور (للإجازات بالأيام فقط)
                    if (!$request['is_hourly'] && !empty($request['start_date']) && !empty($request['end_date'])) {
                        $current_date = new DateTime($request['start_date']);
                        $end_date = new DateTime($request['end_date']);

                        while ($current_date <= $end_date) {
                            $date_str = $current_date->format('Y-m-d');

                            // التحقق من عدم وجود سجل حضور
                            $stmt = $conn->prepare("SELECT id FROM attendance WHERE user_id = ? AND date = ?");
                            $stmt->execute([$request['user_id'], $date_str]);

                            if (!$stmt->fetch()) {
                                $attendance_status = 'إجازة';
                                if ($request['leave_type_name'] === 'إيفاد') {
                                    $attendance_status = 'إيفاد';
                                } elseif ($request['leave_type_name'] === 'إجازة مرضية') {
                                    $attendance_status = 'مرض';
                                }

                                $stmt = $conn->prepare("INSERT INTO attendance (user_id, date, status, notes, created_by) VALUES (?, ?, ?, ?, ?)");
                                $stmt->execute([$request['user_id'], $date_str, $attendance_status, $request['leave_type_name'] . ' موافق عليها', $user['id']]);
                            }

                            $current_date->add(new DateInterval('P1D'));
                        }
                    }

                    $conn->commit();
                    $_SESSION['success'] = 'تم الموافقة على الطلب بنجاح وتحديث الرصيد';
                } else {
                    $_SESSION['error'] = 'لم يتم العثور على الطلب';
                }
            } catch (Exception $e) {
                $conn->rollBack();
                $_SESSION['error'] = 'حدث خطأ أثناء الموافقة على الطلب: ' . $e->getMessage();
            }
        }
        header('Location: leave_requests.php');
        exit();
    }
    
    elseif ($post_action === 'reject' && $user['role'] === 'admin') {
        $id = $_POST['id'] ?? '';
        $rejection_reason = trim($_POST['rejection_reason'] ?? '');
        
        if (!empty($id)) {
            try {
                $stmt = $conn->prepare("UPDATE leave_requests SET status = 'مرفوض', approved_by = ?, approved_at = NOW(), rejection_reason = ? WHERE id = ?");
                $stmt->execute([$user['id'], $rejection_reason, $id]);
                $_SESSION['success'] = 'تم رفض الطلب';
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء رفض الطلب';
            }
        }
        header('Location: leave_requests.php');
        exit();
    }
}

// التحقق من وجود جداول الإجازات
try {
    $stmt = $conn->prepare("SELECT 1 FROM leave_requests LIMIT 1");
    $stmt->execute();
} catch (PDOException $e) {
    if (strpos($e->getMessage(), "doesn't exist") !== false) {
        $_SESSION['error'] = 'جداول الإجازات غير موجودة. يرجى تحديث قاعدة البيانات أولاً.';
        echo "<div class='alert alert-danger text-center'>";
        echo "<h4>⚠️ جداول الإجازات غير موجودة</h4>";
        echo "<p>يجب تحديث قاعدة البيانات أولاً لإضافة جداول الحضور والإجازات</p>";
        echo "<a href='update_attendance_db.php' class='btn btn-primary'>تحديث قاعدة البيانات</a>";
        echo "</div>";
        include 'includes/footer.php';
        exit();
    } else {
        throw $e;
    }
}

// جلب طلبات الإجازات
if ($user['role'] === 'admin') {
    $stmt = $conn->prepare("
        SELECT lr.*, u.full_name, lt.name as leave_type_name, approver.full_name as approved_by_name
        FROM leave_requests lr
        JOIN users u ON lr.user_id = u.id
        JOIN leave_types lt ON lr.leave_type_id = lt.id
        LEFT JOIN users approver ON lr.approved_by = approver.id
        ORDER BY lr.created_at DESC
    ");
    $stmt->execute();
} else {
    $stmt = $conn->prepare("
        SELECT lr.*, u.full_name, lt.name as leave_type_name, approver.full_name as approved_by_name
        FROM leave_requests lr
        JOIN users u ON lr.user_id = u.id
        JOIN leave_types lt ON lr.leave_type_id = lt.id
        LEFT JOIN users approver ON lr.approved_by = approver.id
        WHERE lr.user_id = ?
        ORDER BY lr.created_at DESC
    ");
    $stmt->execute([$user['id']]);
}
$leave_requests = $stmt->fetchAll();

// جلب أنواع الإجازات مرتبة حسب الأولوية
$stmt = $conn->prepare("SELECT * FROM leave_types ORDER BY priority_order, name");
$stmt->execute();
$leave_types = $stmt->fetchAll();

$page_title = 'طلبات الإجازات';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-file-alt me-2"></i>
                طلبات الإجازات
            </h1>
            <div class="btn-group">
                <?php if ($action !== 'add'): ?>
                    <a href="leave_requests.php?action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        طلب إجازة جديد
                    </a>
                <?php endif; ?>
                <?php if ($user['role'] === 'admin'): ?>
                    <a href="attendance.php" class="btn btn-info">
                        <i class="fas fa-calendar-check me-2"></i>
                        إدارة الحضور
                    </a>
                    <a href="leave_balance.php" class="btn btn-warning">
                        <i class="fas fa-chart-pie me-2"></i>
                        رصيد الإجازات
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if ($action === 'add'): ?>
<!-- نموذج طلب إجازة جديد -->
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-plus me-2"></i>
                طلب إجازة جديد
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="leave_type_id" class="form-label">نوع الإجازة *</label>
                            <select class="form-select" id="leave_type_id" name="leave_type_id" required onchange="updateLeaveTypeInfo()">
                                <option value="">اختر نوع الإجازة</option>
                                <?php foreach ($leave_types as $type): ?>
                                    <option value="<?php echo $type['id']; ?>"
                                            data-deductible="<?php echo $type['is_deductible']; ?>"
                                            data-unlimited="<?php echo $type['is_unlimited']; ?>"
                                            data-hourly="<?php echo $type['can_be_hourly']; ?>"
                                            data-color="<?php echo $type['color_code']; ?>"
                                            data-icon="<?php echo $type['icon']; ?>">
                                        <i class="<?php echo $type['icon']; ?>"></i>
                                        <?php echo htmlspecialchars($type['name']); ?>
                                        <?php if ($type['is_unlimited']): ?>
                                            (غير محدود)
                                        <?php else: ?>
                                            (<?php echo $type['max_days_per_year']; ?> يوم سنوياً)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="priority" class="form-label">الأولوية</label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="عادي">عادي</option>
                                <option value="عاجل">عاجل</option>
                                <option value="طارئ">طارئ</option>
                            </select>
                        </div>
                    </div>

                    <!-- معلومات نوع الإجازة -->
                    <div id="leave_type_info" class="alert alert-info" style="display: none;">
                        <div class="row">
                            <div class="col-md-4">
                                <strong>يستقطع من الرصيد:</strong> <span id="deductible_info">-</span>
                            </div>
                            <div class="col-md-4">
                                <strong>غير محدود:</strong> <span id="unlimited_info">-</span>
                            </div>
                            <div class="col-md-4">
                                <strong>يمكن بالساعة:</strong> <span id="hourly_info">-</span>
                            </div>
                        </div>
                    </div>

                    <!-- خيار الإجازة بالساعة -->
                    <div class="row mb-3" id="hourly_option" style="display: none;">
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_hourly" name="is_hourly" onchange="toggleHourlyFields()">
                                <label class="form-check-label" for="is_hourly">
                                    <i class="fas fa-clock me-2"></i>
                                    طلب إجازة بالساعة
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- حقول التواريخ للإجازات العادية -->
                    <div class="row" id="date_fields">
                        <div class="col-md-6 mb-3">
                            <label for="start_date" class="form-label">تاريخ البداية *</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" onchange="calculateDays()">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="end_date" class="form-label">تاريخ النهاية *</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" onchange="calculateDays()">
                        </div>

                        <div class="col-md-12 mb-3">
                            <label class="form-label">عدد الأيام</label>
                            <input type="text" class="form-control" id="days_display" readonly placeholder="سيتم حسابها تلقائياً">
                        </div>
                    </div>

                    <!-- حقول الساعات للإجازات بالساعة -->
                    <div class="row" id="hourly_fields" style="display: none;">
                        <div class="col-md-4 mb-3">
                            <label for="hours_count" class="form-label">عدد الساعات *</label>
                            <input type="number" class="form-control" id="hours_count" name="hours_count"
                                   min="0.5" max="8" step="0.5" placeholder="مثال: 2.5">
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="start_time" class="form-label">وقت البداية *</label>
                            <input type="time" class="form-control" id="start_time" name="start_time" onchange="calculateHours()">
                        </div>

                        <div class="col-md-4 mb-3">
                            <label for="end_time" class="form-label">وقت النهاية *</label>
                            <input type="time" class="form-control" id="end_time" name="end_time" onchange="calculateHours()">
                        </div>

                        <div class="col-md-12 mb-3">
                            <label for="emergency_type" class="form-label">نوع الحالة الطارئة</label>
                            <select class="form-select" id="emergency_type" name="emergency_type">
                                <option value="">اختر نوع الحالة</option>
                                <option value="طبية">حالة طبية طارئة</option>
                                <option value="عائلية">ظرف عائلي طارئ</option>
                                <option value="شخصية">ظرف شخصي طارئ</option>
                                <option value="رسمية">مهمة رسمية عاجلة</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reason" class="form-label">سبب الإجازة</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" 
                                  placeholder="اكتب سبب طلب الإجازة..."></textarea>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>
                            تقديم الطلب
                        </button>
                        <a href="leave_requests.php" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// بيانات أنواع الإجازات
const leaveTypesData = <?php echo json_encode($leave_types); ?>;

function updateLeaveTypeInfo() {
    const selectElement = document.getElementById('leave_type_id');
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const infoDiv = document.getElementById('leave_type_info');
    const hourlyOption = document.getElementById('hourly_option');

    if (selectedOption.value) {
        const isDeductible = selectedOption.dataset.deductible === '1';
        const isUnlimited = selectedOption.dataset.unlimited === '1';
        const canBeHourly = selectedOption.dataset.hourly === '1';

        // تحديث معلومات نوع الإجازة
        document.getElementById('deductible_info').textContent = isDeductible ? 'نعم' : 'لا';
        document.getElementById('unlimited_info').textContent = isUnlimited ? 'نعم' : 'لا';
        document.getElementById('hourly_info').textContent = canBeHourly ? 'نعم' : 'لا';

        // إظهار معلومات نوع الإجازة
        infoDiv.style.display = 'block';

        // إظهار/إخفاء خيار الإجازة بالساعة
        if (canBeHourly) {
            hourlyOption.style.display = 'block';
        } else {
            hourlyOption.style.display = 'none';
            document.getElementById('is_hourly').checked = false;
            toggleHourlyFields();
        }

        // تغيير لون الحد حسب نوع الإجازة
        const color = selectedOption.dataset.color;
        selectElement.style.borderColor = color;
        infoDiv.style.borderColor = color;
    } else {
        infoDiv.style.display = 'none';
        hourlyOption.style.display = 'none';
        selectElement.style.borderColor = '';
    }
}

function toggleHourlyFields() {
    const isHourly = document.getElementById('is_hourly').checked;
    const dateFields = document.getElementById('date_fields');
    const hourlyFields = document.getElementById('hourly_fields');
    const startDateField = document.getElementById('start_date');
    const endDateField = document.getElementById('end_date');

    if (isHourly) {
        dateFields.style.display = 'none';
        hourlyFields.style.display = 'block';

        // إزالة required من حقول التاريخ
        startDateField.removeAttribute('required');
        endDateField.removeAttribute('required');

        // إضافة required لحقول الساعات
        document.getElementById('hours_count').setAttribute('required', 'required');
        document.getElementById('start_time').setAttribute('required', 'required');
        document.getElementById('end_time').setAttribute('required', 'required');

        // تعيين تاريخ اليوم للإجازة بالساعة
        const today = new Date().toISOString().split('T')[0];
        startDateField.value = today;
        endDateField.value = today;
    } else {
        dateFields.style.display = 'block';
        hourlyFields.style.display = 'none';

        // إضافة required لحقول التاريخ
        startDateField.setAttribute('required', 'required');
        endDateField.setAttribute('required', 'required');

        // إزالة required من حقول الساعات
        document.getElementById('hours_count').removeAttribute('required');
        document.getElementById('start_time').removeAttribute('required');
        document.getElementById('end_time').removeAttribute('required');

        // مسح قيم الساعات
        document.getElementById('hours_count').value = '';
        document.getElementById('start_time').value = '';
        document.getElementById('end_time').value = '';
        document.getElementById('emergency_type').value = '';
    }
}

function calculateDays() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;

    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const timeDiff = end.getTime() - start.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;

        if (daysDiff > 0) {
            document.getElementById('days_display').value = daysDiff + ' يوم';
        } else {
            document.getElementById('days_display').value = 'تاريخ غير صحيح';
        }
    }
}

function calculateHours() {
    const startTime = document.getElementById('start_time').value;
    const endTime = document.getElementById('end_time').value;

    if (startTime && endTime) {
        const start = new Date('2000-01-01 ' + startTime);
        const end = new Date('2000-01-01 ' + endTime);

        if (end > start) {
            const timeDiff = end.getTime() - start.getTime();
            const hours = timeDiff / (1000 * 3600);
            document.getElementById('hours_count').value = hours.toFixed(1);
        } else {
            document.getElementById('hours_count').value = '';
            alert('وقت النهاية يجب أن يكون بعد وقت البداية');
        }
    }
}

// تطبيق الإعدادات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تعيين الحد الأدنى للتاريخ (اليوم)
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('start_date').setAttribute('min', today);
    document.getElementById('end_date').setAttribute('min', today);
});
</script>

<?php else: ?>
<!-- قائمة طلبات الإجازات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>
                    <i class="fas fa-list me-2"></i>
                    قائمة طلبات الإجازات (<?php echo count($leave_requests); ?> طلب)
                </span>
                <button class="btn btn-sm btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-1"></i>
                    طباعة
                </button>
            </div>
            <div class="card-body">
                <?php if (empty($leave_requests)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد طلبات إجازات</h5>
                        <p class="text-muted">لم يتم تقديم أي طلبات إجازات بعد</p>
                        <a href="leave_requests.php?action=add" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            طلب إجازة جديد
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <?php if ($user['role'] === 'admin'): ?>
                                        <th>المنتسب</th>
                                    <?php endif; ?>
                                    <th>نوع الإجازة</th>
                                    <th>النوع</th>
                                    <th>المدة</th>
                                    <th>التوقيت</th>
                                    <th>الأولوية</th>
                                    <th>السبب</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الطلب</th>
                                    <?php if ($user['role'] === 'admin'): ?>
                                        <th>الإجراءات</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($leave_requests as $request): ?>
                                    <tr>
                                        <?php if ($user['role'] === 'admin'): ?>
                                            <td><strong><?php echo htmlspecialchars($request['full_name']); ?></strong></td>
                                        <?php endif; ?>
                                        <td>
                                            <?php
                                            // جلب معلومات نوع الإجازة
                                            $stmt = $conn->prepare("SELECT color_code, icon FROM leave_types WHERE id = ?");
                                            $stmt->execute([$request['leave_type_id']]);
                                            $leave_type_info = $stmt->fetch();
                                            $color = $leave_type_info['color_code'] ?? '#007bff';
                                            $icon = $leave_type_info['icon'] ?? 'fas fa-calendar';
                                            ?>
                                            <span class="badge" style="background-color: <?php echo $color; ?>">
                                                <i class="<?php echo $icon; ?> me-1"></i>
                                                <?php echo htmlspecialchars($request['leave_type_name']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($request['is_hourly']): ?>
                                                <span class="badge bg-warning text-dark">
                                                    <i class="fas fa-clock me-1"></i>
                                                    بالساعة
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-primary">
                                                    <i class="fas fa-calendar-day me-1"></i>
                                                    بالأيام
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($request['is_hourly']): ?>
                                                <strong><?php echo $request['hours_count']; ?> ساعة</strong>
                                            <?php else: ?>
                                                <strong><?php echo $request['days_count']; ?> يوم</strong>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($request['is_hourly']): ?>
                                                <?php echo date('Y/m/d', strtotime($request['start_date'])); ?><br>
                                                <small class="text-muted">
                                                    <?php echo date('H:i', strtotime($request['start_time'])); ?> -
                                                    <?php echo date('H:i', strtotime($request['end_time'])); ?>
                                                </small>
                                            <?php else: ?>
                                                <?php echo date('Y/m/d', strtotime($request['start_date'])); ?> -
                                                <?php echo date('Y/m/d', strtotime($request['end_date'])); ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $priority_colors = [
                                                'عادي' => 'secondary',
                                                'عاجل' => 'warning',
                                                'طارئ' => 'danger'
                                            ];
                                            $priority_icons = [
                                                'عادي' => 'fas fa-minus',
                                                'عاجل' => 'fas fa-exclamation',
                                                'طارئ' => 'fas fa-exclamation-triangle'
                                            ];
                                            ?>
                                            <span class="badge bg-<?php echo $priority_colors[$request['priority']] ?? 'secondary'; ?>">
                                                <i class="<?php echo $priority_icons[$request['priority']] ?? 'fas fa-minus'; ?> me-1"></i>
                                                <?php echo $request['priority']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $reason_text = htmlspecialchars($request['reason']);
                                            if ($request['emergency_type']) {
                                                $reason_text = '<strong>' . $request['emergency_type'] . ':</strong> ' . $reason_text;
                                            }
                                            echo substr($reason_text, 0, 50) . (strlen($reason_text) > 50 ? '...' : '');
                                            ?>
                                        </td>
                                        <td>
                                            <?php
                                            $status_colors = [
                                                'معلق' => 'warning',
                                                'موافق' => 'success',
                                                'مرفوض' => 'danger'
                                            ];
                                            ?>
                                            <span class="badge bg-<?php echo $status_colors[$request['status']] ?? 'secondary'; ?>">
                                                <?php echo $request['status']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('Y/m/d H:i', strtotime($request['created_at'])); ?></td>
                                        <?php if ($user['role'] === 'admin'): ?>
                                            <td>
                                                <?php if ($request['status'] === 'معلق'): ?>
                                                    <div class="btn-group btn-group-sm">
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="approve">
                                                            <input type="hidden" name="id" value="<?php echo $request['id']; ?>">
                                                            <button type="submit" class="btn btn-success" title="موافقة"
                                                                    onclick="return confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        </form>
                                                        <button class="btn btn-danger" title="رفض"
                                                                onclick="showRejectModal(<?php echo $request['id']; ?>)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                <?php else: ?>
                                                    <small class="text-muted">
                                                        <?php if ($request['approved_by_name']): ?>
                                                            بواسطة: <?php echo htmlspecialchars($request['approved_by_name']); ?>
                                                        <?php endif; ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                        <?php endif; ?>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- نافذة رفض الطلب -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفض طلب الإجازة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="">
                <div class="modal-body">
                    <input type="hidden" name="action" value="reject">
                    <input type="hidden" name="id" id="reject_request_id">
                    
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">سبب الرفض</label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3" 
                                  placeholder="اكتب سبب رفض الطلب..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-2"></i>
                        رفض الطلب
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showRejectModal(requestId) {
    document.getElementById('reject_request_id').value = requestId;
    document.getElementById('rejection_reason').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
    modal.show();
}
</script>

<?php include 'includes/footer.php'; ?>
