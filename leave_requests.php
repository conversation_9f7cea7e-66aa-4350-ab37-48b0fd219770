<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkLogin();

$user = getCurrentUser();
$conn = getDBConnection();
$action = $_GET['action'] ?? 'list';
$request_id = $_GET['id'] ?? null;

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $post_action = $_POST['action'] ?? '';
    
    if ($post_action === 'add') {
        $leave_type_id = $_POST['leave_type_id'] ?? '';
        $start_date = $_POST['start_date'] ?? '';
        $end_date = $_POST['end_date'] ?? '';
        $reason = trim($_POST['reason'] ?? '');
        
        if (!empty($leave_type_id) && !empty($start_date) && !empty($end_date)) {
            // حساب عدد الأيام
            $start = new DateTime($start_date);
            $end = new DateTime($end_date);
            $days_count = $end->diff($start)->days + 1;
            
            try {
                $stmt = $conn->prepare("INSERT INTO leave_requests (user_id, leave_type_id, start_date, end_date, days_count, reason) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->execute([$user['id'], $leave_type_id, $start_date, $end_date, $days_count, $reason]);
                $_SESSION['success'] = 'تم تقديم طلب الإجازة بنجاح';
                header('Location: leave_requests.php');
                exit();
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء تقديم الطلب';
            }
        } else {
            $_SESSION['error'] = 'يرجى ملء جميع الحقول المطلوبة';
        }
    }
    
    elseif ($post_action === 'approve' && $user['role'] === 'admin') {
        $id = $_POST['id'] ?? '';
        if (!empty($id)) {
            try {
                $conn->beginTransaction();
                
                // الحصول على تفاصيل الطلب
                $stmt = $conn->prepare("SELECT * FROM leave_requests WHERE id = ?");
                $stmt->execute([$id]);
                $request = $stmt->fetch();
                
                if ($request) {
                    // تحديث حالة الطلب
                    $stmt = $conn->prepare("UPDATE leave_requests SET status = 'موافق', approved_by = ?, approved_at = NOW() WHERE id = ?");
                    $stmt->execute([$user['id'], $id]);
                    
                    // تحديث رصيد الإجازات
                    $stmt = $conn->prepare("UPDATE annual_leave_balance SET used_days = used_days + ?, remaining_days = remaining_days - ? WHERE user_id = ? AND year = ? AND leave_type_id = ?");
                    $stmt->execute([$request['days_count'], $request['days_count'], $request['user_id'], date('Y'), $request['leave_type_id']]);
                    
                    // إضافة سجلات الحضور للأيام المطلوبة
                    $current_date = new DateTime($request['start_date']);
                    $end_date = new DateTime($request['end_date']);
                    
                    while ($current_date <= $end_date) {
                        $date_str = $current_date->format('Y-m-d');
                        
                        // التحقق من عدم وجود سجل حضور
                        $stmt = $conn->prepare("SELECT id FROM attendance WHERE user_id = ? AND date = ?");
                        $stmt->execute([$request['user_id'], $date_str]);
                        
                        if (!$stmt->fetch()) {
                            $stmt = $conn->prepare("INSERT INTO attendance (user_id, date, status, notes, created_by) VALUES (?, ?, 'إجازة', ?, ?)");
                            $stmt->execute([$request['user_id'], $date_str, 'إجازة موافق عليها', $user['id']]);
                        }
                        
                        $current_date->add(new DateInterval('P1D'));
                    }
                    
                    $conn->commit();
                    $_SESSION['success'] = 'تم الموافقة على الطلب بنجاح';
                } else {
                    $_SESSION['error'] = 'لم يتم العثور على الطلب';
                }
            } catch (Exception $e) {
                $conn->rollBack();
                $_SESSION['error'] = 'حدث خطأ أثناء الموافقة على الطلب';
            }
        }
        header('Location: leave_requests.php');
        exit();
    }
    
    elseif ($post_action === 'reject' && $user['role'] === 'admin') {
        $id = $_POST['id'] ?? '';
        $rejection_reason = trim($_POST['rejection_reason'] ?? '');
        
        if (!empty($id)) {
            try {
                $stmt = $conn->prepare("UPDATE leave_requests SET status = 'مرفوض', approved_by = ?, approved_at = NOW(), rejection_reason = ? WHERE id = ?");
                $stmt->execute([$user['id'], $rejection_reason, $id]);
                $_SESSION['success'] = 'تم رفض الطلب';
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء رفض الطلب';
            }
        }
        header('Location: leave_requests.php');
        exit();
    }
}

// جلب طلبات الإجازات
if ($user['role'] === 'admin') {
    $stmt = $conn->prepare("
        SELECT lr.*, u.full_name, lt.name as leave_type_name, approver.full_name as approved_by_name
        FROM leave_requests lr 
        JOIN users u ON lr.user_id = u.id 
        JOIN leave_types lt ON lr.leave_type_id = lt.id
        LEFT JOIN users approver ON lr.approved_by = approver.id
        ORDER BY lr.created_at DESC
    ");
    $stmt->execute();
} else {
    $stmt = $conn->prepare("
        SELECT lr.*, u.full_name, lt.name as leave_type_name, approver.full_name as approved_by_name
        FROM leave_requests lr 
        JOIN users u ON lr.user_id = u.id 
        JOIN leave_types lt ON lr.leave_type_id = lt.id
        LEFT JOIN users approver ON lr.approved_by = approver.id
        WHERE lr.user_id = ?
        ORDER BY lr.created_at DESC
    ");
    $stmt->execute([$user['id']]);
}
$leave_requests = $stmt->fetchAll();

// جلب أنواع الإجازات
$stmt = $conn->prepare("SELECT * FROM leave_types ORDER BY name");
$stmt->execute();
$leave_types = $stmt->fetchAll();

$page_title = 'طلبات الإجازات';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-file-alt me-2"></i>
                طلبات الإجازات
            </h1>
            <div class="btn-group">
                <?php if ($action !== 'add'): ?>
                    <a href="leave_requests.php?action=add" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        طلب إجازة جديد
                    </a>
                <?php endif; ?>
                <?php if ($user['role'] === 'admin'): ?>
                    <a href="attendance.php" class="btn btn-info">
                        <i class="fas fa-calendar-check me-2"></i>
                        إدارة الحضور
                    </a>
                    <a href="leave_balance.php" class="btn btn-warning">
                        <i class="fas fa-chart-pie me-2"></i>
                        رصيد الإجازات
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if ($action === 'add'): ?>
<!-- نموذج طلب إجازة جديد -->
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-plus me-2"></i>
                طلب إجازة جديد
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="leave_type_id" class="form-label">نوع الإجازة *</label>
                            <select class="form-select" id="leave_type_id" name="leave_type_id" required>
                                <option value="">اختر نوع الإجازة</option>
                                <?php foreach ($leave_types as $type): ?>
                                    <option value="<?php echo $type['id']; ?>">
                                        <?php echo htmlspecialchars($type['name']); ?> 
                                        (<?php echo $type['max_days_per_year']; ?> يوم سنوياً)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label">عدد الأيام</label>
                            <input type="text" class="form-control" id="days_display" readonly placeholder="سيتم حسابها تلقائياً">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="start_date" class="form-label">تاريخ البداية *</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" required onchange="calculateDays()">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="end_date" class="form-label">تاريخ النهاية *</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" required onchange="calculateDays()">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reason" class="form-label">سبب الإجازة</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" 
                                  placeholder="اكتب سبب طلب الإجازة..."></textarea>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>
                            تقديم الطلب
                        </button>
                        <a href="leave_requests.php" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function calculateDays() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const timeDiff = end.getTime() - start.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
        
        if (daysDiff > 0) {
            document.getElementById('days_display').value = daysDiff + ' يوم';
        } else {
            document.getElementById('days_display').value = 'تاريخ غير صحيح';
        }
    }
}
</script>

<?php else: ?>
<!-- قائمة طلبات الإجازات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>
                    <i class="fas fa-list me-2"></i>
                    قائمة طلبات الإجازات (<?php echo count($leave_requests); ?> طلب)
                </span>
                <button class="btn btn-sm btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-1"></i>
                    طباعة
                </button>
            </div>
            <div class="card-body">
                <?php if (empty($leave_requests)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد طلبات إجازات</h5>
                        <p class="text-muted">لم يتم تقديم أي طلبات إجازات بعد</p>
                        <a href="leave_requests.php?action=add" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            طلب إجازة جديد
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <?php if ($user['role'] === 'admin'): ?>
                                        <th>المنتسب</th>
                                    <?php endif; ?>
                                    <th>نوع الإجازة</th>
                                    <th>من - إلى</th>
                                    <th>عدد الأيام</th>
                                    <th>السبب</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الطلب</th>
                                    <?php if ($user['role'] === 'admin'): ?>
                                        <th>الإجراءات</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($leave_requests as $request): ?>
                                    <tr>
                                        <?php if ($user['role'] === 'admin'): ?>
                                            <td><strong><?php echo htmlspecialchars($request['full_name']); ?></strong></td>
                                        <?php endif; ?>
                                        <td>
                                            <span class="badge bg-info"><?php echo htmlspecialchars($request['leave_type_name']); ?></span>
                                        </td>
                                        <td>
                                            <?php echo date('Y/m/d', strtotime($request['start_date'])); ?> - 
                                            <?php echo date('Y/m/d', strtotime($request['end_date'])); ?>
                                        </td>
                                        <td><span class="badge bg-secondary"><?php echo $request['days_count']; ?> يوم</span></td>
                                        <td><?php echo htmlspecialchars(substr($request['reason'], 0, 50)) . (strlen($request['reason']) > 50 ? '...' : ''); ?></td>
                                        <td>
                                            <?php
                                            $status_colors = [
                                                'معلق' => 'warning',
                                                'موافق' => 'success',
                                                'مرفوض' => 'danger'
                                            ];
                                            ?>
                                            <span class="badge bg-<?php echo $status_colors[$request['status']] ?? 'secondary'; ?>">
                                                <?php echo $request['status']; ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('Y/m/d', strtotime($request['created_at'])); ?></td>
                                        <?php if ($user['role'] === 'admin'): ?>
                                            <td>
                                                <?php if ($request['status'] === 'معلق'): ?>
                                                    <div class="btn-group btn-group-sm">
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="approve">
                                                            <input type="hidden" name="id" value="<?php echo $request['id']; ?>">
                                                            <button type="submit" class="btn btn-success" title="موافقة" 
                                                                    onclick="return confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        </form>
                                                        <button class="btn btn-danger" title="رفض" 
                                                                onclick="showRejectModal(<?php echo $request['id']; ?>)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                <?php else: ?>
                                                    <small class="text-muted">
                                                        <?php if ($request['approved_by_name']): ?>
                                                            بواسطة: <?php echo htmlspecialchars($request['approved_by_name']); ?>
                                                        <?php endif; ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                        <?php endif; ?>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- نافذة رفض الطلب -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">رفض طلب الإجازة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="">
                <div class="modal-body">
                    <input type="hidden" name="action" value="reject">
                    <input type="hidden" name="id" id="reject_request_id">
                    
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">سبب الرفض</label>
                        <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3" 
                                  placeholder="اكتب سبب رفض الطلب..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-2"></i>
                        رفض الطلب
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showRejectModal(requestId) {
    document.getElementById('reject_request_id').value = requestId;
    document.getElementById('rejection_reason').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
    modal.show();
}
</script>

<?php include 'includes/footer.php'; ?>
