<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkAdmin(); // فقط المدير يمكنه الوصول

$user = getCurrentUser();
$conn = getDBConnection();
$action = $_GET['action'] ?? 'list';
$user_id = $_GET['id'] ?? null;

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $post_action = $_POST['action'] ?? '';
    
    if ($post_action === 'add') {
        $username = trim($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $full_name = trim($_POST['full_name'] ?? '');
        $role = $_POST['role'] ?? 'user';
        
        if (!empty($username) && !empty($password) && !empty($full_name)) {
            try {
                // التحقق من عدم وجود اسم المستخدم
                $stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
                $stmt->execute([$username]);
                if ($stmt->fetch()) {
                    $_SESSION['error'] = 'اسم المستخدم موجود بالفعل';
                } else {
                    $hashed_password = hashPassword($password);
                    $stmt = $conn->prepare("INSERT INTO users (username, password, role, full_name) VALUES (?, ?, ?, ?)");
                    $stmt->execute([$username, $hashed_password, $role, $full_name]);
                    $_SESSION['success'] = 'تم إضافة المنتسب بنجاح';
                }
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء إضافة المنتسب';
            }
        } else {
            $_SESSION['error'] = 'يرجى ملء جميع الحقول المطلوبة';
        }
        header('Location: users_management.php');
        exit();
    }
    
    elseif ($post_action === 'edit') {
        $id = $_POST['id'] ?? '';
        $username = trim($_POST['username'] ?? '');
        $full_name = trim($_POST['full_name'] ?? '');
        $role = $_POST['role'] ?? 'user';
        $new_password = $_POST['new_password'] ?? '';
        
        if (!empty($id) && !empty($username) && !empty($full_name)) {
            try {
                // التحقق من عدم وجود اسم المستخدم لمستخدم آخر
                $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
                $stmt->execute([$username, $id]);
                if ($stmt->fetch()) {
                    $_SESSION['error'] = 'اسم المستخدم موجود بالفعل';
                } else {
                    if (!empty($new_password)) {
                        // تحديث مع كلمة مرور جديدة
                        $hashed_password = hashPassword($new_password);
                        $stmt = $conn->prepare("UPDATE users SET username = ?, password = ?, role = ?, full_name = ? WHERE id = ?");
                        $stmt->execute([$username, $hashed_password, $role, $full_name, $id]);
                    } else {
                        // تحديث بدون تغيير كلمة المرور
                        $stmt = $conn->prepare("UPDATE users SET username = ?, role = ?, full_name = ? WHERE id = ?");
                        $stmt->execute([$username, $role, $full_name, $id]);
                    }
                    $_SESSION['success'] = 'تم تحديث بيانات المنتسب بنجاح';
                }
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء تحديث البيانات';
            }
        } else {
            $_SESSION['error'] = 'يرجى ملء جميع الحقول المطلوبة';
        }
        header('Location: users_management.php');
        exit();
    }
    
    elseif ($post_action === 'delete') {
        $id = $_POST['id'] ?? '';
        if (!empty($id) && $id != $user['id']) { // لا يمكن حذف الحساب الحالي
            try {
                $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
                $stmt->execute([$id]);
                $_SESSION['success'] = 'تم حذف المنتسب بنجاح';
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء حذف المنتسب';
            }
        } else {
            $_SESSION['error'] = 'لا يمكن حذف هذا المنتسب';
        }
        header('Location: users_management.php');
        exit();
    }
    
    elseif ($post_action === 'reset_password') {
        $id = $_POST['id'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        
        if (!empty($id) && !empty($new_password)) {
            try {
                $hashed_password = hashPassword($new_password);
                $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
                $stmt->execute([$hashed_password, $id]);
                $_SESSION['success'] = 'تم تغيير كلمة المرور بنجاح';
            } catch (Exception $e) {
                $_SESSION['error'] = 'حدث خطأ أثناء تغيير كلمة المرور';
            }
        } else {
            $_SESSION['error'] = 'يرجى إدخال كلمة مرور جديدة';
        }
        header('Location: users_management.php');
        exit();
    }
}

// جلب المستخدمين
$stmt = $conn->prepare("SELECT * FROM users ORDER BY role DESC, full_name ASC");
$stmt->execute();
$users = $stmt->fetchAll();

// جلب مستخدم للتعديل
$edit_user = null;
if ($action === 'edit' && $user_id) {
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $edit_user = $stmt->fetch();
    
    if (!$edit_user) {
        $_SESSION['error'] = 'لم يتم العثور على المنتسب';
        header('Location: users_management.php');
        exit();
    }
}

$page_title = 'إدارة المنتسبين';
include 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-users me-2"></i>
                إدارة المنتسبين
            </h1>
            <?php if ($action !== 'add' && $action !== 'edit'): ?>
                <div class="btn-group">
                    <a href="users_management.php?action=add" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة منتسب جديد
                    </a>
                    <a href="bulk_users.php" class="btn btn-success">
                        <i class="fas fa-users-cog me-2"></i>
                        إضافة بالجملة
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php if ($action === 'add' || $action === 'edit'): ?>
<!-- نموذج إضافة/تعديل المنتسب -->
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-<?php echo $action === 'add' ? 'user-plus' : 'user-edit'; ?> me-2"></i>
                <?php echo $action === 'add' ? 'إضافة منتسب جديد' : 'تعديل بيانات المنتسب'; ?>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <input type="hidden" name="action" value="<?php echo $action; ?>">
                    <?php if ($action === 'edit'): ?>
                        <input type="hidden" name="id" value="<?php echo $edit_user['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="full_name" class="form-label">الاسم الكامل *</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                   value="<?php echo htmlspecialchars($edit_user['full_name'] ?? ''); ?>" 
                                   placeholder="مثال: أحمد محمد علي" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">اسم المستخدم *</label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="<?php echo htmlspecialchars($edit_user['username'] ?? ''); ?>" 
                                   placeholder="مثال: ahmed.ali" required>
                            <small class="text-muted">يستخدم لتسجيل الدخول</small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">الدور *</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="user" <?php echo ($edit_user['role'] ?? 'user') === 'user' ? 'selected' : ''; ?>>منتسب</option>
                                <option value="admin" <?php echo ($edit_user['role'] ?? '') === 'admin' ? 'selected' : ''; ?>>مدير</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="<?php echo $action === 'add' ? 'password' : 'new_password'; ?>" class="form-label">
                                <?php echo $action === 'add' ? 'كلمة المرور *' : 'كلمة المرور الجديدة (اختياري)'; ?>
                            </label>
                            <input type="password" class="form-control" 
                                   id="<?php echo $action === 'add' ? 'password' : 'new_password'; ?>" 
                                   name="<?php echo $action === 'add' ? 'password' : 'new_password'; ?>" 
                                   <?php echo $action === 'add' ? 'required' : ''; ?>>
                            <?php if ($action === 'edit'): ?>
                                <small class="text-muted">اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور</small>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            <?php echo $action === 'add' ? 'إضافة المنتسب' : 'حفظ التغييرات'; ?>
                        </button>
                        <a href="users_management.php" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php else: ?>
<!-- قائمة المنتسبين -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>
                    <i class="fas fa-list me-2"></i>
                    قائمة المنتسبين (<?php echo count($users); ?> منتسب)
                </span>
                <div class="btn-group">
                    <a href="users_stats.php" class="btn btn-sm btn-info">
                        <i class="fas fa-chart-pie me-1"></i>
                        الإحصائيات
                    </a>
                    <button class="btn btn-sm btn-outline-primary" onclick="window.print()">
                        <i class="fas fa-print me-1"></i>
                        طباعة
                    </button>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($users)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا يوجد منتسبين مسجلين</h5>
                        <p class="text-muted">ابدأ بإضافة منتسب جديد</p>
                        <a href="users_management.php?action=add" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة منتسب جديد
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الاسم الكامل</th>
                                    <th>اسم المستخدم</th>
                                    <th>الدور</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $u): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($u['full_name']); ?></strong>
                                            <?php if ($u['id'] == $user['id']): ?>
                                                <span class="badge bg-info ms-2">أنت</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <code><?php echo htmlspecialchars($u['username']); ?></code>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo $u['role'] === 'admin' ? 'bg-danger' : 'bg-primary'; ?>">
                                                <?php echo $u['role'] === 'admin' ? 'مدير' : 'منتسب'; ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('Y/m/d', strtotime($u['created_at'])); ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="users_management.php?action=edit&id=<?php echo $u['id']; ?>" 
                                                   class="btn btn-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                
                                                <button class="btn btn-warning" title="إعادة تعيين كلمة المرور" 
                                                        onclick="resetPassword(<?php echo $u['id']; ?>, '<?php echo htmlspecialchars($u['full_name']); ?>')">
                                                    <i class="fas fa-key"></i>
                                                </button>
                                                
                                                <?php if ($u['id'] != $user['id']): ?>
                                                    <form method="POST" style="display: inline;" 
                                                          onsubmit="return confirmDelete('هل أنت متأكد من حذف هذا المنتسب؟ سيتم حذف جميع بياناته!')">
                                                        <input type="hidden" name="action" value="delete">
                                                        <input type="hidden" name="id" value="<?php echo $u['id']; ?>">
                                                        <button type="submit" class="btn btn-danger" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- نافذة إعادة تعيين كلمة المرور -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إعادة تعيين كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="">
                <div class="modal-body">
                    <input type="hidden" name="action" value="reset_password">
                    <input type="hidden" name="id" id="reset_user_id">
                    
                    <div class="mb-3">
                        <label class="form-label">المنتسب:</label>
                        <p class="fw-bold" id="reset_user_name"></p>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">كلمة المرور الجديدة *</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-key me-2"></i>
                        تغيير كلمة المرور
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function resetPassword(userId, userName) {
    document.getElementById('reset_user_id').value = userId;
    document.getElementById('reset_user_name').textContent = userName;
    document.getElementById('new_password').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
}
</script>

<?php include 'includes/footer.php'; ?>
