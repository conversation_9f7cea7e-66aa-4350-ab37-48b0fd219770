<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkLogin();

$user = getCurrentUser();
$conn = getDBConnection();

// الحصول على المعاملات من الرابط
$month = $_GET['month'] ?? date('Y-m');
$user_filter = $_GET['user_id'] ?? '';
$report_type = $_GET['type'] ?? 'monthly';
$print_mode = isset($_GET['print']);

// إعداد شروط البحث
$where_conditions = [];
$params = [];

// فلتر الشهر
if ($report_type === 'monthly') {
    $where_conditions[] = "DATE_FORMAT(date, '%Y-%m') = ?";
    $params[] = $month;
} elseif ($report_type === 'daily') {
    $where_conditions[] = "date = ?";
    $params[] = $_GET['date'] ?? date('Y-m-d');
}

// فلتر المستخدم (للمدير فقط)
if ($user['role'] === 'admin' && !empty($user_filter)) {
    $where_conditions[] = "user_id = ?";
    $params[] = $user_filter;
} elseif ($user['role'] !== 'admin') {
    $where_conditions[] = "user_id = ?";
    $params[] = $user['id'];
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب المهام
$stmt = $conn->prepare("
    SELECT t.*, u.full_name 
    FROM tasks t 
    JOIN users u ON t.user_id = u.id 
    $where_clause
    ORDER BY t.date DESC, t.created_at DESC
");
$stmt->execute($params);
$tasks = $stmt->fetchAll();

// جلب الإنجازات
$stmt = $conn->prepare("
    SELECT a.*, u.full_name 
    FROM achievements a 
    JOIN users u ON a.user_id = u.id 
    $where_clause
    ORDER BY a.date DESC, a.created_at DESC
");
$stmt->execute($params);
$achievements = $stmt->fetchAll();

// حساب الإحصائيات
$total_tasks = count($tasks);
$completed_tasks = count(array_filter($tasks, function($task) { return $task['status'] === 'منجز'; }));
$pending_tasks = $total_tasks - $completed_tasks;
$total_achievements = count($achievements);

// جلب قائمة المستخدمين للمدير
$users_list = [];
if ($user['role'] === 'admin') {
    $stmt = $conn->prepare("SELECT id, full_name FROM users ORDER BY full_name");
    $stmt->execute();
    $users_list = $stmt->fetchAll();
}

$page_title = 'التقارير الشهرية';
if (!$print_mode) {
    include 'includes/header.php';
}
?>

<?php if ($print_mode): ?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير شهري - شعبة السيطرة والإنترنت</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #667eea;
            margin: 0;
            font-size: 2rem;
        }
        
        .header h2 {
            color: #666;
            margin: 10px 0;
            font-size: 1.5rem;
        }
        
        .report-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #667eea;
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-title {
            background: #667eea;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: right;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .status-completed {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
        }
        
        .status-pending {
            background: #ffc107;
            color: #333;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
        }
        
        .footer {
            margin-top: 50px;
            text-align: center;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        
        @media print {
            body { margin: 0; }
            .no-print { display: none !important; }
        }
    </style>
</head>
<body>
<?php endif; ?>

<?php if (!$print_mode): ?>
<!-- نموذج اختيار التقرير -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar me-2"></i>
                إعدادات التقرير
            </div>
            <div class="card-body">
                <form method="GET" action="">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="type" class="form-label">نوع التقرير</label>
                            <select class="form-select" id="type" name="type">
                                <option value="monthly" <?php echo $report_type === 'monthly' ? 'selected' : ''; ?>>تقرير شهري</option>
                                <option value="daily" <?php echo $report_type === 'daily' ? 'selected' : ''; ?>>تقرير يومي</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3 mb-3" id="month-field" <?php echo $report_type === 'daily' ? 'style="display:none"' : ''; ?>>
                            <label for="month" class="form-label">الشهر</label>
                            <input type="month" class="form-control" id="month" name="month" value="<?php echo $month; ?>">
                        </div>
                        
                        <div class="col-md-3 mb-3" id="date-field" <?php echo $report_type === 'monthly' ? 'style="display:none"' : ''; ?>>
                            <label for="date" class="form-label">التاريخ</label>
                            <input type="date" class="form-control" id="date" name="date" value="<?php echo $_GET['date'] ?? date('Y-m-d'); ?>">
                        </div>
                        
                        <?php if ($user['role'] === 'admin'): ?>
                        <div class="col-md-3 mb-3">
                            <label for="user_id" class="form-label">المنتسب</label>
                            <select class="form-select" id="user_id" name="user_id">
                                <option value="">جميع المنتسبين</option>
                                <?php foreach ($users_list as $u): ?>
                                    <option value="<?php echo $u['id']; ?>" <?php echo $user_filter == $u['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($u['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            عرض التقرير
                        </button>
                        <a href="reports.php?<?php echo http_build_query(array_merge($_GET, ['print' => '1'])); ?>" 
                           class="btn btn-success" target="_blank">
                            <i class="fas fa-print me-2"></i>
                            طباعة التقرير
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('type').addEventListener('change', function() {
    const monthField = document.getElementById('month-field');
    const dateField = document.getElementById('date-field');
    
    if (this.value === 'monthly') {
        monthField.style.display = 'block';
        dateField.style.display = 'none';
    } else {
        monthField.style.display = 'none';
        dateField.style.display = 'block';
    }
});
</script>
<?php endif; ?>

<!-- محتوى التقرير -->
<div class="<?php echo $print_mode ? '' : 'card'; ?>">
    <?php if (!$print_mode): ?>
    <div class="card-header">
        <i class="fas fa-file-alt me-2"></i>
        <?php echo $report_type === 'monthly' ? 'التقرير الشهري' : 'التقرير اليومي'; ?>
    </div>
    <div class="card-body">
    <?php endif; ?>
    
    <?php if ($print_mode): ?>
    <!-- رأس التقرير للطباعة -->
    <div class="header">
        <h1>شعبة السيطرة والإنترنت</h1>
        <h2><?php echo $report_type === 'monthly' ? 'التقرير الشهري' : 'التقرير اليومي'; ?></h2>
    </div>
    
    <div class="report-info">
        <div>
            <strong>الفترة:</strong> 
            <?php 
            if ($report_type === 'monthly') {
                echo date('Y/m', strtotime($month . '-01'));
            } else {
                echo date('Y/m/d', strtotime($_GET['date'] ?? date('Y-m-d')));
            }
            ?>
        </div>
        <div>
            <strong>تاريخ الإنشاء:</strong> <?php echo date('Y/m/d H:i'); ?>
        </div>
        <?php if ($user['role'] === 'admin' && !empty($user_filter)): ?>
        <div>
            <strong>المنتسب:</strong>
            <?php
            $selected_user = array_filter($users_list, function($u) use ($user_filter) {
                return $u['id'] == $user_filter;
            });
            echo !empty($selected_user) ? htmlspecialchars(reset($selected_user)['full_name']) : 'غير محدد';
            ?>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>
    
    <!-- الإحصائيات -->
    <div class="<?php echo $print_mode ? 'stats-grid' : 'row mb-4'; ?>">
        <div class="<?php echo $print_mode ? 'stat-card' : 'col-md-3'; ?>">
            <?php if (!$print_mode): ?>
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h3><?php echo $total_tasks; ?></h3>
                    <p class="mb-0">إجمالي المهام</p>
                </div>
            </div>
            <?php else: ?>
            <div class="stat-number"><?php echo $total_tasks; ?></div>
            <div>إجمالي المهام</div>
            <?php endif; ?>
        </div>
        
        <div class="<?php echo $print_mode ? 'stat-card' : 'col-md-3'; ?>">
            <?php if (!$print_mode): ?>
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h3><?php echo $completed_tasks; ?></h3>
                    <p class="mb-0">المهام المكتملة</p>
                </div>
            </div>
            <?php else: ?>
            <div class="stat-number"><?php echo $completed_tasks; ?></div>
            <div>المهام المكتملة</div>
            <?php endif; ?>
        </div>
        
        <div class="<?php echo $print_mode ? 'stat-card' : 'col-md-3'; ?>">
            <?php if (!$print_mode): ?>
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h3><?php echo $pending_tasks; ?></h3>
                    <p class="mb-0">المهام المعلقة</p>
                </div>
            </div>
            <?php else: ?>
            <div class="stat-number"><?php echo $pending_tasks; ?></div>
            <div>المهام المعلقة</div>
            <?php endif; ?>
        </div>
        
        <div class="<?php echo $print_mode ? 'stat-card' : 'col-md-3'; ?>">
            <?php if (!$print_mode): ?>
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h3><?php echo $total_achievements; ?></h3>
                    <p class="mb-0">إجمالي الإنجازات</p>
                </div>
            </div>
            <?php else: ?>
            <div class="stat-number"><?php echo $total_achievements; ?></div>
            <div>إجمالي الإنجازات</div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- جدول المهام -->
    <div class="<?php echo $print_mode ? 'section' : 'mb-4'; ?>">
        <h4 class="<?php echo $print_mode ? 'section-title' : 'mb-3'; ?>">
            <i class="fas fa-tasks me-2"></i>
            المهام اليومية (<?php echo count($tasks); ?>)
        </h4>
        
        <?php if (empty($tasks)): ?>
            <p class="text-muted text-center py-3">لا توجد مهام في هذه الفترة</p>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>عنوان المهمة</th>
                            <th>نوع العمل</th>
                            <th>الحالة</th>
                            <?php if ($user['role'] === 'admin' && empty($user_filter)): ?>
                                <th>المنتسب</th>
                            <?php endif; ?>
                            <th>التفاصيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($tasks as $task): ?>
                            <tr>
                                <td><?php echo date('Y/m/d', strtotime($task['date'])); ?></td>
                                <td><?php echo htmlspecialchars($task['title']); ?></td>
                                <td><?php echo htmlspecialchars($task['type']); ?></td>
                                <td>
                                    <span class="<?php echo $print_mode ? ($task['status'] === 'منجز' ? 'status-completed' : 'status-pending') : 'badge ' . ($task['status'] === 'منجز' ? 'bg-success' : 'bg-warning'); ?>">
                                        <?php echo $task['status']; ?>
                                    </span>
                                </td>
                                <?php if ($user['role'] === 'admin' && empty($user_filter)): ?>
                                    <td><?php echo htmlspecialchars($task['full_name']); ?></td>
                                <?php endif; ?>
                                <td><?php echo htmlspecialchars(substr($task['details'], 0, 100)) . (strlen($task['details']) > 100 ? '...' : ''); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- جدول الإنجازات -->
    <div class="<?php echo $print_mode ? 'section' : 'mb-4'; ?>">
        <h4 class="<?php echo $print_mode ? 'section-title' : 'mb-3'; ?>">
            <i class="fas fa-trophy me-2"></i>
            الإنجازات (<?php echo count($achievements); ?>)
        </h4>
        
        <?php if (empty($achievements)): ?>
            <p class="text-muted text-center py-3">لا توجد إنجازات في هذه الفترة</p>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>عنوان الإنجاز</th>
                            <?php if ($user['role'] === 'admin' && empty($user_filter)): ?>
                                <th>المنتسب</th>
                            <?php endif; ?>
                            <th>التفاصيل</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($achievements as $achievement): ?>
                            <tr>
                                <td><?php echo date('Y/m/d', strtotime($achievement['date'])); ?></td>
                                <td><?php echo htmlspecialchars($achievement['title']); ?></td>
                                <?php if ($user['role'] === 'admin' && empty($user_filter)): ?>
                                    <td><?php echo htmlspecialchars($achievement['full_name']); ?></td>
                                <?php endif; ?>
                                <td><?php echo htmlspecialchars($achievement['details']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
    
    <?php if ($print_mode): ?>
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام إدارة شعبة السيطرة والإنترنت</p>
        <p>تاريخ الطباعة: <?php echo date('Y/m/d H:i:s'); ?></p>
    </div>
    
    <script>
        window.onload = function() {
            window.print();
        }
    </script>
    <?php endif; ?>
    
    <?php if (!$print_mode): ?>
    </div>
    <?php endif; ?>
</div>

<?php if ($print_mode): ?>
</body>
</html>
<?php else: ?>
<?php include 'includes/footer.php'; ?>
<?php endif; ?>
