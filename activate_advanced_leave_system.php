<?php
// تفعيل نظام الإجازات المتطور
require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>تفعيل نظام الإجازات المتطور</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }";
echo ".container { background: rgba(255,255,255,0.95); color: #333; padding: 40px; border-radius: 15px; box-shadow: 0 0 30px rgba(0,0,0,0.3); max-width: 900px; margin: 0 auto; }";
echo ".success { color: green; padding: 10px; background: #e8f5e8; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: red; padding: 10px; background: #ffe6e6; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: blue; padding: 10px; background: #e8f4fd; border-radius: 5px; margin: 10px 0; }";
echo ".warning { color: orange; padding: 10px; background: #fff3cd; border-radius: 5px; margin: 10px 0; }";
echo ".btn { background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; margin: 8px; display: inline-block; font-weight: bold; }";
echo ".btn-success { background: #28a745; }";
echo ".btn-warning { background: #ffc107; color: black; }";
echo ".feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }";
echo ".feature-card { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 15px; border-radius: 8px; border-left: 4px solid #667eea; }";
echo ".step-card { background: white; border: 2px solid #28a745; border-radius: 10px; padding: 20px; margin: 15px 0; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1 style='text-align: center; color: #667eea; margin-bottom: 30px;'>🚀 تفعيل نظام الإجازات المتطور</h1>";

$steps_completed = 0;
$total_steps = 5;

try {
    $conn = getDBConnection();
    echo "<div class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // الخطوة 1: التحقق من جداول الحضور
    echo "<div class='step-card'>";
    echo "<h3>📅 الخطوة 1: التحقق من جداول الحضور والإجازات</h3>";
    
    $required_tables = ['attendance', 'leave_types', 'leave_requests', 'annual_leave_balance'];
    $missing_tables = [];
    
    foreach ($required_tables as $table) {
        try {
            $stmt = $conn->prepare("SELECT 1 FROM $table LIMIT 1");
            $stmt->execute();
            echo "<div class='success'>✅ جدول $table موجود</div>";
        } catch (Exception $e) {
            $missing_tables[] = $table;
            echo "<div class='error'>❌ جدول $table غير موجود</div>";
        }
    }
    
    if (empty($missing_tables)) {
        $steps_completed++;
        echo "<div class='success'>✅ جميع الجداول المطلوبة موجودة</div>";
    } else {
        echo "<div class='warning'>⚠️ يجب إنشاء الجداول المفقودة أولاً</div>";
        echo "<p><a href='setup_attendance.php' class='btn btn-warning'>إنشاء جداول الحضور</a></p>";
    }
    echo "</div>";
    
    // الخطوة 2: تحديث جداول الإجازات
    if (empty($missing_tables)) {
        echo "<div class='step-card'>";
        echo "<h3>🔄 الخطوة 2: تحديث جداول الإجازات</h3>";
        
        // التحقق من وجود الحقول الجديدة
        $new_fields_exist = true;
        try {
            $stmt = $conn->prepare("SELECT is_deductible FROM leave_types LIMIT 1");
            $stmt->execute();
            echo "<div class='success'>✅ حقول نظام الإجازات المتطور موجودة</div>";
            $steps_completed++;
        } catch (Exception $e) {
            $new_fields_exist = false;
            echo "<div class='warning'>⚠️ يجب تحديث جداول الإجازات</div>";
            echo "<p><a href='update_leave_system.php' class='btn btn-warning'>تحديث نظام الإجازات</a></p>";
        }
        echo "</div>";
        
        // الخطوة 3: التحقق من أنواع الإجازات الجديدة
        if ($new_fields_exist) {
            echo "<div class='step-card'>";
            echo "<h3>📋 الخطوة 3: التحقق من أنواع الإجازات</h3>";
            
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM leave_types WHERE name = 'ساعة طارئة'");
            $stmt->execute();
            $emergency_hour_exists = $stmt->fetch()['count'] > 0;
            
            if ($emergency_hour_exists) {
                echo "<div class='success'>✅ أنواع الإجازات المتطورة موجودة</div>";
                $steps_completed++;
                
                // عرض أنواع الإجازات
                $stmt = $conn->prepare("SELECT name, is_unlimited, is_deductible, can_be_hourly, color_code, icon FROM leave_types ORDER BY priority_order");
                $stmt->execute();
                $leave_types = $stmt->fetchAll();
                
                echo "<div class='feature-grid'>";
                foreach ($leave_types as $type) {
                    echo "<div class='feature-card' style='border-left-color: " . $type['color_code'] . ";'>";
                    echo "<h5 style='color: " . $type['color_code'] . ";'>";
                    echo "<i class='" . $type['icon'] . " me-2'></i>" . $type['name'];
                    echo "</h5>";
                    echo "<ul style='font-size: 0.9rem; margin: 0; padding-left: 20px;'>";
                    echo "<li>" . ($type['is_unlimited'] ? 'غير محدود' : 'محدود') . "</li>";
                    echo "<li>" . ($type['is_deductible'] ? 'يستقطع من الرصيد' : 'لا يستقطع') . "</li>";
                    echo "<li>" . ($type['can_be_hourly'] ? 'يمكن بالساعة' : 'بالأيام فقط') . "</li>";
                    echo "</ul>";
                    echo "</div>";
                }
                echo "</div>";
            } else {
                echo "<div class='warning'>⚠️ أنواع الإجازات المتطورة غير موجودة</div>";
                echo "<p><a href='update_leave_system.php' class='btn btn-warning'>تحديث أنواع الإجازات</a></p>";
            }
            echo "</div>";
            
            // الخطوة 4: التحقق من أرصدة الإجازات
            if ($emergency_hour_exists) {
                echo "<div class='step-card'>";
                echo "<h3>💰 الخطوة 4: التحقق من أرصدة الإجازات</h3>";
                
                $stmt = $conn->prepare("SELECT COUNT(*) as count FROM annual_leave_balance WHERE year = ?");
                $stmt->execute([date('Y')]);
                $balances_count = $stmt->fetch()['count'];
                
                if ($balances_count > 0) {
                    echo "<div class='success'>✅ أرصدة الإجازات موجودة ($balances_count رصيد)</div>";
                    $steps_completed++;
                } else {
                    echo "<div class='warning'>⚠️ لا توجد أرصدة إجازات للسنة الحالية</div>";
                    echo "<p><a href='update_leave_system.php' class='btn btn-warning'>تهيئة أرصدة الإجازات</a></p>";
                }
                echo "</div>";
                
                // الخطوة 5: اختبار النظام
                if ($balances_count > 0) {
                    echo "<div class='step-card'>";
                    echo "<h3>🧪 الخطوة 5: اختبار النظام</h3>";
                    
                    // اختبار إنشاء طلب إجازة تجريبي
                    try {
                        $stmt = $conn->prepare("SELECT id FROM users WHERE role = 'user' LIMIT 1");
                        $stmt->execute();
                        $test_user = $stmt->fetch();
                        
                        if ($test_user) {
                            echo "<div class='success'>✅ يوجد منتسبين للاختبار</div>";
                            echo "<div class='success'>✅ النظام جاهز للاستخدام</div>";
                            $steps_completed++;
                        } else {
                            echo "<div class='warning'>⚠️ لا يوجد منتسبين في النظام</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='error'>❌ خطأ في اختبار النظام</div>";
                    }
                    echo "</div>";
                }
            }
        }
    }
    
    // عرض ملخص التفعيل
    echo "<div style='background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; border-radius: 15px; margin: 40px 0; text-align: center;'>";
    echo "<h2>📊 ملخص التفعيل</h2>";
    echo "<div style='font-size: 2rem; margin: 20px 0;'>";
    echo "$steps_completed / $total_steps خطوات مكتملة";
    echo "</div>";
    
    $completion_percentage = round(($steps_completed / $total_steps) * 100);
    echo "<div style='background: rgba(255,255,255,0.2); border-radius: 10px; padding: 10px; margin: 20px 0;'>";
    echo "<div style='background: white; height: 20px; border-radius: 10px; overflow: hidden;'>";
    echo "<div style='background: #28a745; height: 100%; width: $completion_percentage%; transition: width 0.3s ease;'></div>";
    echo "</div>";
    echo "<p style='margin: 10px 0;'>$completion_percentage% مكتمل</p>";
    echo "</div>";
    echo "</div>";
    
    if ($steps_completed >= $total_steps) {
        // النظام جاهز
        echo "<div style='background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 30px 0;'>";
        echo "<h3 style='color: #28a745;'>🎉 نظام الإجازات المتطور جاهز!</h3>";
        
        echo "<div class='feature-grid'>";
        
        echo "<div class='feature-card'>";
        echo "<h4>📝 طلبات الإجازات المتطورة</h4>";
        echo "<ul>";
        echo "<li>إجازة سنوية (30 يوم)</li>";
        echo "<li>إجازة مرضية (غير محدودة)</li>";
        echo "<li>حالة وفاة (غير محدودة)</li>";
        echo "<li>إيفاد (غير محدود)</li>";
        echo "<li>ساعة طارئة (بالساعة)</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div class='feature-card'>";
        echo "<h4>⚡ مميزات متقدمة</h4>";
        echo "<ul>";
        echo "<li>طلبات بالساعة للحالات الطارئة</li>";
        echo "<li>ترتيب حسب الأولوية</li>";
        echo "<li>ألوان مميزة لكل نوع</li>";
        echo "<li>استقطاع ذكي من الرصيد</li>";
        echo "<li>تتبع دقيق للاستخدام</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div class='feature-card'>";
        echo "<h4>🎯 نظام ذكي</h4>";
        echo "<ul>";
        echo "<li>الإجازة السنوية تستقطع من الرصيد</li>";
        echo "<li>المرضية والوفاة لا تستقطع</li>";
        echo "<li>الإيفاد لا يؤثر على الرصيد</li>";
        echo "<li>الساعات الطارئة مرنة</li>";
        echo "<li>تحديث تلقائي للحضور</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "</div>";
        echo "</div>";
        
        // روابط سريعة
        echo "<div style='text-align: center; margin: 40px 0;'>";
        echo "<h3>🚀 ابدأ الاستخدام:</h3>";
        echo "<a href='leave_requests.php' class='btn btn-success'>📝 طلبات الإجازات المتطورة</a>";
        echo "<a href='leave_balance.php' class='btn btn-success'>💰 رصيد الإجازات الذكي</a>";
        echo "<a href='attendance.php' class='btn btn-success'>📅 إدارة الحضور</a>";
        echo "<a href='dashboard.php' class='btn'>🏠 لوحة التحكم</a>";
        echo "</div>";
        
    } else {
        // النظام يحتاج تكملة
        echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 30px 0;'>";
        echo "<h3 style='color: #856404;'>⚠️ يحتاج إكمال الخطوات</h3>";
        echo "<p>يرجى إكمال الخطوات المتبقية لتفعيل النظام بالكامل</p>";
        
        if (in_array('attendance', $missing_tables)) {
            echo "<p><a href='setup_attendance.php' class='btn btn-warning'>1. إنشاء جداول الحضور</a></p>";
        }
        if (!$new_fields_exist) {
            echo "<p><a href='update_leave_system.php' class='btn btn-warning'>2. تحديث نظام الإجازات</a></p>";
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في التفعيل: " . $e->getMessage() . "</div>";
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>💡 خطوات الإصلاح:</h3>";
    echo "<ol>";
    echo "<li>تأكد من تشغيل XAMPP (Apache + MySQL)</li>";
    echo "<li>تأكد من وجود قاعدة البيانات control_internet_db</li>";
    echo "<li>قم بتشغيل setup_attendance.php أولاً</li>";
    echo "<li>ثم قم بتشغيل update_leave_system.php</li>";
    echo "</ol>";
    echo "</div>";
}

echo "</div>";
echo "</body>";
echo "</html>";
?>
