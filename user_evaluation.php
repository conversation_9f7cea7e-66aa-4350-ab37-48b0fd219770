<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

checkLogin();

$user = getCurrentUser();
$conn = getDBConnection();

// التأكد من أن المستخدم منتسب وليس مدير
if ($user['role'] === 'admin') {
    header('Location: evaluations.php');
    exit();
}

$evaluation_id = $_GET['id'] ?? null;
$print_mode = isset($_GET['print']);

// جلب التقييمات الخاصة بالمنتسب
try {
    $stmt = $conn->prepare("
        SELECT e.*, admin.full_name as evaluated_by_name
        FROM evaluations e 
        JOIN users admin ON e.evaluated_by = admin.id
        WHERE e.user_id = ?
        ORDER BY e.month DESC
    ");
    $stmt->execute([$user['id']]);
    $evaluations = $stmt->fetchAll();
} catch (PDOException $e) {
    if (strpos($e->getMessage(), "doesn't exist") !== false) {
        $_SESSION['error'] = 'جدول التقييمات غير موجود. يرجى التواصل مع المدير.';
        header('Location: user_dashboard.php');
        exit();
    } else {
        throw $e;
    }
}

// جلب تقييم محدد للعرض
$view_evaluation = null;
if ($evaluation_id) {
    foreach ($evaluations as $eval) {
        if ($eval['id'] == $evaluation_id) {
            $view_evaluation = $eval;
            break;
        }
    }
    
    if (!$view_evaluation) {
        $_SESSION['error'] = 'لم يتم العثور على التقييم';
        header('Location: user_evaluation.php');
        exit();
    }
}

$page_title = 'تقييماتي';
if (!$print_mode) {
    include 'includes/header.php';
}
?>

<?php if ($print_mode && $view_evaluation): ?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقييم المنتسب - <?php echo htmlspecialchars($user['full_name']); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }
        
        .print-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 0 30px rgba(0,0,0,0.2);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #667eea;
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        
        .header h2 {
            color: #666;
            margin: 10px 0;
            font-size: 1.5rem;
        }
        
        .evaluation-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 5px solid #667eea;
        }
        
        .score-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin: 30px 0;
        }
        
        .score-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .score-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .final-score {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin: 30px 0;
        }
        
        .final-score .score {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .grade-badge {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 1.2rem;
            font-weight: bold;
            margin-top: 10px;
        }
        
        .grade-excellent { background: #28a745; color: white; }
        .grade-very-good { background: #007bff; color: white; }
        .grade-good { background: #17a2b8; color: white; }
        .grade-acceptable { background: #ffc107; color: black; }
        .grade-weak { background: #dc3545; color: white; }
        
        .notes-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .signature-section {
            margin-top: 50px;
            padding-top: 30px;
            border-top: 2px solid #667eea;
            text-align: center;
        }
        
        .signature-line {
            border-bottom: 2px solid #333;
            width: 200px;
            margin: 20px auto;
            padding-bottom: 5px;
        }
        
        @media print {
            body { margin: 0; background: white !important; }
            .print-container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="header">
            <h1>شعبة السيطرة والإنترنت</h1>
            <h2>تقييم أداء المنتسب</h2>
        </div>
        
        <div class="evaluation-info">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <strong>اسم المنتسب:</strong> <?php echo htmlspecialchars($user['full_name']); ?><br>
                    <strong>الشهر المقيم:</strong> <?php echo date('Y/m', strtotime($view_evaluation['month'])); ?><br>
                    <strong>تاريخ التقييم:</strong> <?php echo date('Y/m/d', strtotime($view_evaluation['created_at'])); ?>
                </div>
                <div>
                    <strong>المهام المنجزة:</strong> <?php echo $view_evaluation['tasks_completed']; ?> مهمة<br>
                    <strong>الإنجازات:</strong> <?php echo $view_evaluation['achievements_count']; ?> إنجاز<br>
                    <strong>المقيم:</strong> <?php echo htmlspecialchars($view_evaluation['evaluated_by_name']); ?>
                </div>
            </div>
        </div>
        
        <h3 style="color: #667eea; text-align: center; margin: 30px 0;">تفاصيل الدرجات</h3>
        
        <div class="score-grid">
            <div class="score-card">
                <div class="score-number"><?php echo $view_evaluation['quality_score']; ?></div>
                <div>جودة العمل</div>
                <small>(من 5)</small>
            </div>
            <div class="score-card">
                <div class="score-number"><?php echo $view_evaluation['punctuality_score']; ?></div>
                <div>الالتزام والانضباط</div>
                <small>(من 5)</small>
            </div>
            <div class="score-card">
                <div class="score-number"><?php echo $view_evaluation['cooperation_score']; ?></div>
                <div>التعاون وروح الفريق</div>
                <small>(من 5)</small>
            </div>
            <div class="score-card">
                <div class="score-number"><?php echo $view_evaluation['innovation_score']; ?></div>
                <div>الإبداع والمبادرة</div>
                <small>(من 5)</small>
            </div>
        </div>
        
        <div class="final-score">
            <div class="score"><?php echo number_format($view_evaluation['total_score'], 2); ?>/20</div>
            <div style="font-size: 1.5rem;">المجموع الكلي</div>
            <?php
            $grade_classes = [
                'ممتاز' => 'grade-excellent',
                'جيد جداً' => 'grade-very-good',
                'جيد' => 'grade-good',
                'مقبول' => 'grade-acceptable',
                'ضعيف' => 'grade-weak'
            ];
            ?>
            <div class="grade-badge <?php echo $grade_classes[$view_evaluation['grade']] ?? 'grade-acceptable'; ?>">
                <?php echo $view_evaluation['grade']; ?>
            </div>
        </div>
        
        <?php if (!empty($view_evaluation['notes'])): ?>
        <div class="notes-section">
            <h4 style="color: #856404; margin-bottom: 15px;">
                <i class="fas fa-sticky-note"></i> ملاحظات وتوصيات:
            </h4>
            <p style="margin: 0; font-size: 1.1rem;">
                <?php echo nl2br(htmlspecialchars($view_evaluation['notes'])); ?>
            </p>
        </div>
        <?php endif; ?>
        
        <div class="signature-section">
            <p><strong>مسؤول شعبة السيطرة والإنترنت</strong></p>
            <div class="signature-line"></div>
            <p><strong>محمد حسن محمد</strong></p>
            <p style="margin-top: 30px; color: #666;">
                تاريخ الطباعة: <?php echo date('Y/m/d H:i'); ?>
            </p>
        </div>
    </div>
    
    <script>
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>

<?php else: ?>

<?php if (!$print_mode): ?>
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-star me-2"></i>
                تقييماتي
            </h1>
            <div class="btn-group">
                <a href="user_dashboard.php" class="btn btn-secondary">
                    <i class="fas fa-home me-2"></i>
                    العودة للرئيسية
                </a>
                <a href="print_leave_details.php?user_id=<?php echo $user['id']; ?>" class="btn btn-info">
                    <i class="fas fa-print me-2"></i>
                    طباعة تفاصيل الإجازات
                </a>
            </div>
        </div>
    </div>
</div>

<?php if ($view_evaluation): ?>
<!-- عرض تفاصيل التقييم -->
<div class="row">
    <div class="col-md-10 mx-auto">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>
                    <i class="fas fa-eye me-2"></i>
                    تفاصيل التقييم - <?php echo date('Y/m', strtotime($view_evaluation['month'])); ?>
                </span>
                <a href="user_evaluation.php?id=<?php echo $view_evaluation['id']; ?>&print=1" 
                   class="btn btn-success" target="_blank">
                    <i class="fas fa-print me-2"></i>
                    طباعة التقييم
                </a>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5 class="text-primary">معلومات التقييم</h5>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الشهر:</strong></td>
                                <td><?php echo date('Y/m', strtotime($view_evaluation['month'])); ?></td>
                            </tr>
                            <tr>
                                <td><strong>المهام المنجزة:</strong></td>
                                <td><span class="badge bg-info"><?php echo $view_evaluation['tasks_completed']; ?></span></td>
                            </tr>
                            <tr>
                                <td><strong>الإنجازات:</strong></td>
                                <td><span class="badge bg-success"><?php echo $view_evaluation['achievements_count']; ?></span></td>
                            </tr>
                            <tr>
                                <td><strong>المقيم:</strong></td>
                                <td><?php echo htmlspecialchars($view_evaluation['evaluated_by_name']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ التقييم:</strong></td>
                                <td><?php echo date('Y/m/d H:i', strtotime($view_evaluation['created_at'])); ?></td>
                            </tr>
                        </table>
                    </div>
                    
                    <div class="col-md-6">
                        <h5 class="text-primary">النتيجة النهائية</h5>
                        <div class="text-center">
                            <div class="display-4 text-primary mb-2">
                                <?php echo number_format($view_evaluation['total_score'], 2); ?>/20
                            </div>
                            <?php
                            $grade_colors = [
                                'ممتاز' => 'success',
                                'جيد جداً' => 'primary',
                                'جيد' => 'info',
                                'مقبول' => 'warning',
                                'ضعيف' => 'danger'
                            ];
                            ?>
                            <span class="badge bg-<?php echo $grade_colors[$view_evaluation['grade']] ?? 'secondary'; ?> fs-5 px-4 py-2">
                                <?php echo $view_evaluation['grade']; ?>
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="text-primary">تفاصيل الدرجات</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6>جودة العمل</h6>
                                        <div class="h4 text-primary"><?php echo $view_evaluation['quality_score']; ?>/5</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6>الالتزام والانضباط</h6>
                                        <div class="h4 text-success"><?php echo $view_evaluation['punctuality_score']; ?>/5</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6>التعاون وروح الفريق</h6>
                                        <div class="h4 text-info"><?php echo $view_evaluation['cooperation_score']; ?>/5</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h6>الإبداع والمبادرة</h6>
                                        <div class="h4 text-warning"><?php echo $view_evaluation['innovation_score']; ?>/5</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($view_evaluation['notes'])): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="text-primary">ملاحظات وتوصيات</h5>
                        <div class="alert alert-info">
                            <?php echo nl2br(htmlspecialchars($view_evaluation['notes'])); ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="d-flex gap-2">
                    <a href="user_evaluation.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للقائمة
                    </a>
                    <a href="user_evaluation.php?id=<?php echo $view_evaluation['id']; ?>&print=1" 
                       class="btn btn-success" target="_blank">
                        <i class="fas fa-print me-2"></i>
                        طباعة التقييم
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php else: ?>
<!-- قائمة التقييمات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-list me-2"></i>
                تقييماتي (<?php echo count($evaluations); ?> تقييم)
            </div>
            <div class="card-body">
                <?php if (empty($evaluations)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-star fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد تقييمات</h5>
                        <p class="text-muted">لم يتم تقييمك بعد من قبل المدير</p>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($evaluations as $evaluation): ?>
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div class="badge bg-primary">
                                                <i class="fas fa-calendar me-1"></i>
                                                <?php echo date('Y/m', strtotime($evaluation['month'])); ?>
                                            </div>
                                            <div class="text-end">
                                                <div class="h5 text-primary mb-0"><?php echo number_format($evaluation['total_score'], 2); ?>/20</div>
                                                <small class="text-muted">النتيجة</small>
                                            </div>
                                        </div>
                                        
                                        <?php
                                        $grade_colors = [
                                            'ممتاز' => 'success',
                                            'جيد جداً' => 'primary',
                                            'جيد' => 'info',
                                            'مقبول' => 'warning',
                                            'ضعيف' => 'danger'
                                        ];
                                        ?>
                                        <div class="text-center mb-3">
                                            <span class="badge bg-<?php echo $grade_colors[$evaluation['grade']] ?? 'secondary'; ?> fs-6 px-3 py-2">
                                                <?php echo $evaluation['grade']; ?>
                                            </span>
                                        </div>
                                        
                                        <div class="row text-center small mb-3">
                                            <div class="col-6">
                                                <div class="text-info fw-bold"><?php echo $evaluation['tasks_completed']; ?></div>
                                                <div class="text-muted">مهام</div>
                                            </div>
                                            <div class="col-6">
                                                <div class="text-success fw-bold"><?php echo $evaluation['achievements_count']; ?></div>
                                                <div class="text-muted">إنجازات</div>
                                            </div>
                                        </div>
                                        
                                        <div class="d-flex gap-2">
                                            <a href="user_evaluation.php?id=<?php echo $evaluation['id']; ?>" 
                                               class="btn btn-sm btn-primary flex-fill">
                                                <i class="fas fa-eye me-1"></i>
                                                عرض التفاصيل
                                            </a>
                                            <a href="user_evaluation.php?id=<?php echo $evaluation['id']; ?>&print=1" 
                                               class="btn btn-sm btn-success" target="_blank" title="طباعة">
                                                <i class="fas fa-print"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>
<?php endif; ?>

<?php endif; ?>

<?php if (!$print_mode) include 'includes/footer.php'; ?>
